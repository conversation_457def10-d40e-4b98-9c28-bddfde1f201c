
<!DOCTYPE html>
<html lang="en" theme="dark" bgcolor="black" hgcolor="purple">
    <head>
        <meta charset="utf-8"><title>The Perfect Run Novel Chapters - Novel Fire</title><meta name="description" content="List of the most recent chapters published for the The Perfect Run novel. A total of 130 chapters have been translated and the last update in the novel is Chapter 130: Farewell, New Rome (Ending)"><meta name="copyright" content="Copyright © Novel Fire"><meta name="author" content="Novel Fire"><meta name="robots" content="index,follow"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"><meta property="og:site_name" content="Novel Fire"><meta property="og:type" content="website"><meta property="og:url" content="http://novelfire.net/book/the-perfect-run/chapters?page=2"><meta property="og:title" content="The Perfect Run Novel Chapters - Novel Fire"><meta property="og:description" content="List of the most recent chapters published for the The Perfect Run novel. A total of 130 chapters have been translated and the last update in the novel is Chapter 130: Farewell, New Rome (Ending)"><meta property="og:image" content="https://novelfire.net/server-1/the-perfect-run.jpg"><meta property="og:locale" content="en_US"><meta name="twitter:card" content="summary"><meta name="twitter:url" content="http://novelfire.net/book/the-perfect-run/chapters?page=2"><meta name="twitter:title" content="The Perfect Run Novel Chapters - Novel Fire"><meta name="twitter:description" content="List of the most recent chapters published for the The Perfect Run novel. A total of 130 chapters have been translated and the last update in the novel is Chapter 130: Farewell, New Rome (Ending)"><meta name="twitter:image" content="https://novelfire.net/server-1/the-perfect-run.jpg"><meta name="twitter:creator" content="Novel Fire"><meta name="csrf-token" content="AQw0ZLnRKj4ttZy8jwAv5vu7MHYvEp1gwBoq81b7"><script type="text/javascript" nonce="219482a2af1c427f9b84b8066c6" src="//local.adguard.org?ts=1751941974845&amp;type=content-script&amp;dmn=novelfire.net&amp;url=https%3A%2F%2Fnovelfire.net%2Fbook%2Fthe-perfect-run%2Fchapters%3Fpage%3D2&amp;app=brave.exe&amp;css=3&amp;js=1&amp;rel=1&amp;rji=1&amp;sbe=1"></script>
<script type="text/javascript" nonce="219482a2af1c427f9b84b8066c6" src="//local.adguard.org?ts=1751941974845&amp;name=AdGuard%20Assistant&amp;name=AdGuard%20Extra&amp;name=AdGuard%20Popup%20Blocker&amp;type=user-script"></script><link rel="canonical" href="https://novelfire.net/book/the-perfect-run/chapters"><link rel="shortcut icon" href="https://novelfire.net/logo.ico?v=2"><link rel="apple-touch-icon" href="https://novelfire.net/apple-touch-icon.png?v=2"><link rel="preconnect" href="//fonts.gstatic.com" crossorigin><link rel="preconnect" href="//fonts.googleapis.com" crossorigin><link rel="preload stylesheet" as="style" href="https://fonts.googleapis.com/css?family=Roboto:400,500,600,700|Nunito+Sans:400,500,600,700&display=swap" crossorigin onload="this.rel='stylesheet'">
        <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:400,500,600,700|Nunito+Sans:400,500,600,700&display=swap"></noscript>
        <script type="application/ld+json">{ "@context":"https://schema.org/","@type" : "Organization", "name":"Novel Fire", "url":"https://novelfire.net", "slogan": "Novel Fire - Read Web Novels Online Free", "logo": "https://novelfire.net/logo.svg" }</script>
            <script type="application/ld+json">{ "@context":"https://schema.org/","@type" :"BreadcrumbList",
       "itemListElement":[
                            [{"@type":"ListItem","position":1,"name":"Novel","item":"https://novelfire.net/home"}],
                            [{"@type":"ListItem","position":2,"name":"The Perfect Run","item":"https://novelfire.net/book/the-perfect-run"}],
                            [{"@type":"ListItem","position":3,"name":"The Perfect Run Novel Chapters","item":"https://novelfire.net/book/the-perfect-run/chapters"}]
                         ] }</script>
        <link rel="stylesheet" type="text/css" href="https://novelfire.net/frontend/css/navbar.min.css?ver=1.2.9"><link rel="stylesheet" type="text/css" href="https://novelfire.net/frontend/css/media-mobile.min.css?ver=1.2.9"><link rel="stylesheet" type="text/css" media="screen and (min-width: 768px)" href="https://novelfire.net/frontend/css/media-768.min.css?ver=1.2.9"><link rel="stylesheet" type="text/css" media="screen and (min-width: 1024px)" href="https://novelfire.net/frontend/css/media-1024.min.css?ver=1.2.9"><link rel="stylesheet" type="text/css" media="screen and (min-width: 1270px)" href="https://novelfire.net/frontend/css/media-1270.min.css?ver=1.2.9"><link rel="stylesheet" type="text/css" href="https://novelfire.net/frontend/css/fontello.css?ver=1.2.9">
            <link rel="stylesheet" type="text/css" href="https://novelfire.net/frontend/css/novel.chapter-review.min.css?ver=1.2.9" />
    <link rel="stylesheet" type="text/css" href="https://novelfire.net/frontend/css/pagedlist.css?ver=1.2.9" />
        <link rel="stylesheet" href="https://novelfire.net/frontend/css/style.min.css?ver=1.2.9" type="text/css" media="all">
            </head>
    <body class="fade-out">
        <header class="main-header skiptranslate">
            <div class="wrapper">
                <div class="nav-logo"><a title="Novel Fire - Read Web Novels Online Free" href="https://novelfire.net/home"><img src="https://novelfire.net/logo.svg?v=2" alt="Novel Fire"></a></div>
                <div class="navigation-bar">
                    <nav><span class="lnw-slog fs-14">Your fictional stories hub.</span><ul class="navbar-menu"><li class="nav-item"><a title="Search Novels" href="https://novelfire.net/search" class="nav-link"><i class="icon-search"></i> Search</a></li><li class="nav-item"><a title="Explore The Recently Added Novels" href="https://novelfire.net/genre-all/sort-new/status-all/all-novel" class="nav-link"><i class="icon-th-large"></i> Browse</a></li><li class="nav-item"><a title="Novel Ranking" href="https://novelfire.net/ranking" class="nav-link"><i class="icon-diamond"></i> Ranking</a></li><li class="nav-item"><a title="Check out the recently added novel chapters" href="https://novelfire.net/latest-release-novels" class="nav-link"><i class="icon-book-open"></i> Updates</a></li><li class="nav-item"><a title="Member List" href="https://novelfire.net/user/member-list" class="nav-link"><i><svg xmlns="http://www.w3.org/2000/svg" width="16" height="20" fill="currentColor" viewBox="0 0 640 512"><path d="M96 224c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm448 0c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm32 32h-64c-17.6 0-33.5 7.1-45.1 18.6 40.3 22.1 68.9 62 75.1 109.4h66c17.7 0 32-14.3 32-32v-32c0-35.3-28.7-64-64-64zm-256 0c61.9 0 112-50.1 112-112S381.9 32 320 32 208 82.1 208 144s50.1 112 112 112zm76.8 32h-8.3c-20.8 10-43.9 16-68.5 16s-47.6-6-68.5-16h-8.3C179.6 288 128 339.6 128 403.2V432c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48v-28.8c0-63.6-51.6-115.2-115.2-115.2zm-223.7-13.4C161.5 263.1 145.6 256 128 256H64c-35.3 0-64 28.7-64 64v32c0 17.7 14.3 32 32 32h65.9c6.3-47.4 34.9-87.3 75.2-109.4z"/></svg></i> Members</a></li><li class="nav-item"><a title="Search Novels with Advanced Filtering Function" href="https://novelfire.net/search-adv" class="nav-link"><i class="icon-filter"></i> Filter</a></li><li class="nav-item"><a title="Development Announcements" href="https://novelfire.net/notices" class="nav-link"><i class="icon-megaphone"></i><span> DEV</span></a></li><li class="nav-item"><a class="nightmode_switch" data-tool="night" title="Dark Mode" data-night="0" data-content="Light Theme"><i class="icon-sun"></i></a></li></ul></nav>
                    <div class="login-wrap-nav">
                                                    <a class="nav-link login button" href="#modal" data-close-menu-mobile="1">Login</a>
                                            </div>
                </div>
                <div class="nav-back"></div><button id="mobile-menu-btn"><div id="burger-btn"></div></button><span class="nav notify-bell mobile-block icon-bell-alt"></span>
            </div>
        </header>
        <div class="sidebar-wrapper"></div><main role="main">    <div class="navbar-breadcrumb">
        <div class="breadcrumb show-dots container">
            <a title="Novel Fire - Read Web Novels Online Free" href="https://novelfire.net/home"><svg class="svg-home" height="20" viewBox="0 0 24 24" width="20"><path d="M0 0h24v24H0z" fill="none"/><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg> Novel</a>
            <svg width="10" height="10" fill="currentColor" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/></svg>
            <a title="The Perfect Run" href="https://novelfire.net/book/the-perfect-run">The Perfect Run</a>
            <svg width="10" height="10" fill="currentColor" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/></svg>
            <a title="The Perfect Run Novel Chapters" href="https://novelfire.net/book/the-perfect-run/chapters">Chapters</a>
        </div>
    </div>
    <article id="chapter-list-page">
        <header class="container">
            <div class="novel-item">
                <div class="cover-wrap">
                    <a title="The Perfect Run" href="https://novelfire.net/book/the-perfect-run">
                        <figure class="novel-cover">
                            <img src="https://novelfire.net/server-1/the-perfect-run.jpg" alt="The Perfect Run">
                        </figure>
                    </a>
                </div>
                <div class="item-body">
                    <h1>
                        <a class="text2row" title="The Perfect Run" href="https://novelfire.net/book/the-perfect-run">The Perfect Run</a>
                    </h1>
                    <div class="novel-stats">
                        <span>Updated <time datetime="2023-09-11 02:38">1 year ago</time></span>
                    </div>
                    <div class="novel-stats">
                        Status:  <strong class="status">Completed</strong>                     </div>
                </div>
            </div>
            <span class="divider"></span>
            <h2>The Perfect Run Novel Chapters </h2>
            <p>List of most recent chapters published for the The Perfect Run novel. A total of 130 chapters have been translated and the release date of the last chapter is Sep 11, 2023</p>
            <p>Latest Release: <a href="https://novelfire.net/book/the-perfect-run/chapter-130" title="Chapter 130: Farewell, New Rome (Ending)">Chapter 130: Farewell, New Rome (Ending)</a></p>
        </header>
        <div class="novel-body container">
            <section id="info">
                <div id="chpagedlist" class="mb-5">
                    <div class="filters">
                        <div id="gotochap">
                            <input id="gotochapno" class="n-page" name="chapno" type="number" min="1" max="130" placeholder="Enter Chapter No">
                            <input class="button go-page px-4 pointer" type="button" value="Go">
                        </div>
                        <div class="pagenav">
                            <div class="pagination-container"><nav>
        <ul class="pagination">
            
                            <li class="page-item">
                    <a class="page-link" href="https://novelfire.net/book/the-perfect-run/chapters?page=1" rel="prev" aria-label="&laquo; Previous">&lsaquo;</a>
                </li>
            
            
                            
                
                
                                                                                        <li class="page-item"><a class="page-link" href="https://novelfire.net/book/the-perfect-run/chapters?page=1">1</a></li>
                                                                                                <li class="page-item active" aria-current="page"><span class="page-link">2</span></li>
                                                                        
            
                            <li class="page-item disabled" aria-disabled="true" aria-label="Next &raquo;">
                    <span class="page-link" aria-hidden="true">&rsaquo;</span>
                </li>
                    </ul>
    </nav>
</div>
                            <svg aria-hidden="true" style="position: absolute; width: 0px; height: 0px; overflow: hidden;">
                                <symbol id="i-rank-up" viewBox="0 0 1308 1024">
                                    <path d="M512 149.33333366666665h796.444444v113.777777H512V149.33333366666665z m0 341.333333h568.888889v113.777778H512V490.6666666666667z m0 341.333333h341.333333v113.777778H512v-113.777778zM227.555556 303.6159996666667L100.124444 452.9493336666667 13.653333 379.0506666666667 341.333333-4.949333333333332V1002.6666666666666H227.555556V303.6159996666667z"></path>
                                </symbol>
                            </svg>
                                                            <a href="javascript:;" class="sort-order"><i class="chorder fas asc" data-order="desc"><svg><use xlink:href="#i-rank-up"></use></svg></i></a>
                                                    </div>
                    </div>
                    <ul class="chapter-list">
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-101" title="Chapter 101: Lover's Breath">
                                                                            <span class="chapter-no">101</span>
                                                                        <strong class="chapter-title">Chapter 101: Lover's Breath</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:18:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-102" title="Chapter 102: Moving On">
                                                                            <span class="chapter-no">102</span>
                                                                        <strong class="chapter-title">Chapter 102: Moving On</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:19:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-103" title="Chapter 103: Drug Bust">
                                                                            <span class="chapter-no">103</span>
                                                                        <strong class="chapter-title">Chapter 103: Drug Bust</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:20:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-104" title="Chapter 104: Return of the Cashmere">
                                                                            <span class="chapter-no">104</span>
                                                                        <strong class="chapter-title">Chapter 104: Return of the Cashmere</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:21:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-105" title="Chapter 105: Miracle Cure">
                                                                            <span class="chapter-no">105</span>
                                                                        <strong class="chapter-title">Chapter 105: Miracle Cure</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:22:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-106" title="Chapter 106: Mice Trap">
                                                                            <span class="chapter-no">106</span>
                                                                        <strong class="chapter-title">Chapter 106: Mice Trap</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:23:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-107" title="Chapter 107: Black Dinner">
                                                                            <span class="chapter-no">107</span>
                                                                        <strong class="chapter-title">Chapter 107: Black Dinner</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:24:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-108" title="Chapter 108: Go Daddy Go">
                                                                            <span class="chapter-no">108</span>
                                                                        <strong class="chapter-title">Chapter 108: Go Daddy Go</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:25:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-109" title="Chapter 109: Carnival Town">
                                                                            <span class="chapter-no">109</span>
                                                                        <strong class="chapter-title">Chapter 109: Carnival Town</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:26:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-110" title="Chapter 110: The Truth Beneath the Ice">
                                                                            <span class="chapter-no">110</span>
                                                                        <strong class="chapter-title">Chapter 110: The Truth Beneath the Ice</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:27:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-111" title="Chapter 111: Past Fragment: Origins of the Species">
                                                                            <span class="chapter-no">111</span>
                                                                        <strong class="chapter-title">Chapter 111: Past Fragment: Origins of the Species</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:28:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-112" title="Chapter 112: The Thing">
                                                                            <span class="chapter-no">112</span>
                                                                        <strong class="chapter-title">Chapter 112: The Thing</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:29:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-113" title="Chapter 113: Final Contact">
                                                                            <span class="chapter-no">113</span>
                                                                        <strong class="chapter-title">Chapter 113: Final Contact</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:30:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-114" title="Chapter 114: Chemical Reaction">
                                                                            <span class="chapter-no">114</span>
                                                                        <strong class="chapter-title">Chapter 114: Chemical Reaction</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:31:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-115" title="Chapter 115: The Clone War">
                                                                            <span class="chapter-no">115</span>
                                                                        <strong class="chapter-title">Chapter 115: The Clone War</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:32:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-116" title="Chapter 116: Couple Therapy">
                                                                            <span class="chapter-no">116</span>
                                                                        <strong class="chapter-title">Chapter 116: Couple Therapy</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:33:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-117" title="Chapter 117: Making Waves">
                                                                            <span class="chapter-no">117</span>
                                                                        <strong class="chapter-title">Chapter 117: Making Waves</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:34:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-118" title="Chapter 118: The Last Quest">
                                                                            <span class="chapter-no">118</span>
                                                                        <strong class="chapter-title">Chapter 118: The Last Quest</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:35:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-119" title="Chapter 119: The Last Save">
                                                                            <span class="chapter-no">119</span>
                                                                        <strong class="chapter-title">Chapter 119: The Last Save</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:36:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-120" title="Chapter 120: Speedrunners">
                                                                            <span class="chapter-no">120</span>
                                                                        <strong class="chapter-title">Chapter 120: Speedrunners</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:37:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-121" title="Chapter 121: Presidential Debate">
                                                                            <span class="chapter-no">121</span>
                                                                        <strong class="chapter-title">Chapter 121: Presidential Debate</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:38:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-122" title="Chapter 122: Second Chances">
                                                                            <span class="chapter-no">122</span>
                                                                        <strong class="chapter-title">Chapter 122: Second Chances</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:39:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-123" title="Chapter 123: The Last Break">
                                                                            <span class="chapter-no">123</span>
                                                                        <strong class="chapter-title">Chapter 123: The Last Break</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:40:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-124" title="Chapter 124: Lab Closure">
                                                                            <span class="chapter-no">124</span>
                                                                        <strong class="chapter-title">Chapter 124: Lab Closure</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:41:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-125" title="Chapter 125: Fusion & Fission">
                                                                            <span class="chapter-no">125</span>
                                                                        <strong class="chapter-title">Chapter 125: Fusion & Fission</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:42:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-126" title="Chapter 126: The End is Nigh">
                                                                            <span class="chapter-no">126</span>
                                                                        <strong class="chapter-title">Chapter 126: The End is Nigh</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:43:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-127" title="Chapter 127: Theomachia">
                                                                            <span class="chapter-no">127</span>
                                                                        <strong class="chapter-title">Chapter 127: Theomachia</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:44:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-128" title="Chapter 128: Time & Thunder">
                                                                            <span class="chapter-no">128</span>
                                                                        <strong class="chapter-title">Chapter 128: Time & Thunder</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:45:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-129" title="Chapter 129: The Gate & The Key">
                                                                            <span class="chapter-no">129</span>
                                                                        <strong class="chapter-title">Chapter 129: The Gate & The Key</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:46:41">1 year ago</time>
                                </a>
                            </li>
                                                    <li>
                                <a href="https://novelfire.net/book/the-perfect-run/chapter-130" title="Chapter 130: Farewell, New Rome (Ending)">
                                                                            <span class="chapter-no">130</span>
                                                                        <strong class="chapter-title">Chapter 130: Farewell, New Rome (Ending)</strong>
                                    <time class="chapter-update" datetime="2023-09-10 04:47:41">1 year ago</time>
                                </a>
                            </li>
                                            </ul>
                </div>
            </section>
        </div>
    </article>
    </main>
        <footer><div class="wrapper skiptranslate"><div class="w-100"><div class="col-ft-1"><div class="logo text-center"><a title="Novel Fire - Read Web Novels Online Free" href="https://novelfire.net/home" style="display:inline-block"><img class="lazy footer-logo" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="https://novelfire.net/logo.svg?v=5" alt="Novel Fire"></a></div></div><div class="col-ft-4 mt-0 mt-lg-3 mb-3 mb-lg-0"><div><p>Novel Fire is created in hopes that every novel fan can get to read novel without paying a dime, this site is completely free of charge. To read novel online for free, all you need to do is to visit Novel Fire, search for the novel you want to watch, and enjoy reading it at no cost and with no risk.</div></div><div class="col-ft-2 mb-3 mb-lg-0"><h5>Userful links</h5><nav class="links"><ul><li><a title="Explore the Top Rated Novels" href="https://novelfire.net/ranking">Novel Ranking</a></li><li><a title="Explore The Recently Added Novels" href="https://novelfire.net/genre-all/sort-new/status-all/all-novel">Latest Novels</a></li><li><a title="Recently Added Novel Chapters" href="https://novelfire.net/latest-release-novels">Latest Chapters</a></li><li><a title="Completed Novels" href="https://novelfire.net/genre-all/sort-popular/status-completed/all-novel">Completed Novels</a></li><li><a title="Explore All Novel Tags" href="https://novelfire.net/all-tags/A">All Tags</a></li></ul></nav></div><div class="col-ft-2 mb-3 mb-lg-0"><h5>Page</h5><nav class="links"><ul><li><a title="Privacy Policy" href="https://novelfire.net/page/privacy-policy">Privacy Policy</a></li><li><a title="Terms of Service" href="https://novelfire.net/page/terms-of-service">Terms of Service</a></li><li><a title="Contact Us" href="https://novelfire.net/contact-us">Contact Us</a></li></ul></nav></div></div><div class="clearfix copyright py-2 w-100 text-center"><p>Made with ♥ for Novel Lovers</p><p class="fs-14">Disclaimer: This site Novel Fire does not store any files on its server. All contents are provided by non-affiliated third parties.</p></div></div></footer><script src="https://cdnjs.cloudflare.com/ajax/libs/js-cookie/3.0.1/js.cookie.min.js" integrity="sha512-wT7uPE7tOP6w4o28u1DN775jYjHQApdBnib5Pho4RB0Pgd9y7eSkAV1BTqQydupYDB9GBhTcQQzyNMPMV3cAew==" crossorigin="anonymous" referrerpolicy="no-referrer"></script><script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js" integrity="sha512-bLT0Qm9VnAYZDflyKcBaQ2gg0hSYNQrJ8RilYldYQ1FxQYoCLtUjuuRuZo+fjqhx/qtq/1itJ0C2ejDxltZVFg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script><script src="https://novelfire.net/frontend/js/appsettings.min.js?ver=1.2.9"></script><script src="https://novelfire.net/frontend/js/app.min.js?ver=1.2.9"></script><div class="ajax_waiting"></div><a id="back-to-top" href="#"><svg width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg" class="bi bi-arrow-up-short" style="color: rgb(255, 255, 255);"><path fill-rule="evenodd" d="M8 12a.5.5 0 0 0 .5-.5V5.707l2.146 2.147a.5.5 0 0 0 .708-.708l-3-3a.5.5 0 0 0-.708 0l-3 3a.5.5 0 1 0 .708.708L7.5 5.707V11.5a.5.5 0 0 0 .5.5z"></path></svg></a><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="position: absolute; width: 0; height: 0" id="__SVG_SPRITE_NODE__"><symbol id="i-times" viewBox="0 0 1024 1024"><path d="M618.775 512l320.329-320.329c30.51-30.51 30.51-76.269 0-106.775s-76.269-30.51-106.775 0l-320.329 320.329-320.329-320.329c-30.51-30.51-76.269-30.51-106.775 0s-30.51 76.269 0 106.775l320.329 320.329-320.329 320.329c-30.51 30.51-30.51 76.269 0 106.775s76.269 30.51 106.775 0l320.329-320.329 320.329 320.329c30.51 30.51 76.269 30.51 106.775 0s30.51-76.269 0-106.775l-320.329-320.329z"></path></symbol><symbol id="icon-pantool" viewBox="0 0 24 24"><path d="M21.5 4c-.83 0-1.5.67-1.5 1.5v5c0 .28-.22.5-.5.5s-.5-.22-.5-.5v-8c0-.83-.67-1.5-1.5-1.5S16 1.67 16 2.5v8c0 .28-.22.5-.5.5s-.5-.22-.5-.5v-9c0-.83-.67-1.5-1.5-1.5S12 .67 12 1.5v8.99c0 .28-.22.5-.5.5s-.5-.22-.5-.5V4.5c0-.83-.67-1.5-1.5-1.5S8 3.67 8 4.5v11.41l-4.12-2.35c-.58-.33-1.3-.24-1.78.22-.6.58-.62 1.54-.03 2.13l6.78 6.89c.75.77 1.77 1.2 2.85 1.2H19c2.21 0 4-1.79 4-4V5.5c0-.83-.67-1.5-1.5-1.5z"></path></symbol></svg>
    <div id="modal" class="popupContainer" style="display:none"><header class="popupHeader"><span class="header_title">Login</span><span class="modal_close"><svg><use xlink:href="#i-times"></use></svg></span></header><section class="popupBody"><div class="social_login"><div class="notification"></div><div><a href="https://novelfire.net/auth/redirect/google" class="social_box google"><span class="icon"><i class="i-googlePlus"></i></span><span class="icon_title">LOG IN WITH GOOGLE</span></a><a href="javascript:void(0)" id="login_form" class="social_box"><span class="icon"><i class="i-mail"></i></span><span class="icon_title">LOG IN WITH EMAIL</span></a></div><div class="centeredText">Don't have an account?<br><a href="javascript:void(0)" id="register_form" class="font-weight">Sign up with your email address.</a></div></div><div class="user_login edit-form"><form><label>Email</label><input type="email"><div class="alert alert-email"></div><label>Password</label><input type="password"><div class="alert alert-password"></div><div class="checkbox" style="display:flex"><input id="remember" type="checkbox"><label for="remember">Remember Me</label><label style="margin-left:auto"><a href="javascript:void(0)" id="forgot_password" class="forgot_password">Forgot Password?</a></label></div><div class="action_btns"><div class="one_half"><a href="javascript:void(0)" class="button btn-modal back_btn"><i class="icon-left-big"></i> Back</a></div><div class="one_half last"><a href="javascript:void(0)" class="button btn-modal" onclick="loginAjax()">Login</a></div></div></form></div><div class="user_register edit-form"><form><label>Email</label><input type="email"><div class="alert alert-email"></div><label>Username</label><input type="text"><div class="alert alert-name"></div><label>Password</label><input type="password"><div class="alert alert-password"></div><label>Confirm Password</label><input type="password" name="confirm-password"><div class="alert alert-confirm-password"></div><div class="action_btns"><div class="one_half"><a href="javascript:void(0)" class="button btn-modal back_btn"><i class="icon-left-big"></i> Back</a></div><div class="one_half last"><a href="javascript:void(0)" class="button btn-modal" onclick="registerAjax()">Sign Up</a></div></div></form></div><div class="user_forgot_password edit-form"><form><div class="pb-2"><p style="padding-bottom:10px">Enter your email address that you used to register. We'll send you an email with a link to reset your password.</p><p><i>If you don’t see the email, check other places it might be, like your junk, spam, social, or other folders.</i></p></div><input type="email" placeholder="Email"><div class="alert alert-email"></div><div class="action_btns"><div class="one_half"><a href="javascript:void(0)" class="button btn-modal back_btn_from_forgot_password"><i class="icon-left-big"></i> Back</a></div><div class="one_half last"><a href="javascript:void(0)" class="button btn-modal" onclick="codePasswordResetAjax()">Send</a></div></div></form></div><div class="form_alert"><form><div class="alert"></div></form></div></section></div>
    <script src="https://novelfire.net/frontend/js/modal.min.js?ver=1.2.9"></script>
 <script>window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}gtag('js', new Date());gtag('config', 'G-9YW3E218EG');</script>
         <script>
            $(".sort-order i").click(function(e) {
                window.location.replace(window.location.href.split('?')[0] + '?page=' + $('.n-page').val() + '&sort_by=' + $(this).data('order'));
            });
            function goToElementListChapter(){
                $('html,body').animate({
                    scrollTop: $(".novel-body").offset().top - 75},
                    'slow');
            }

                            goToElementListChapter();
            
            function goToChapter(chapterNumber) {
                if (chapterNumber && chapterNumber <= 130) {
                    window.location.href = 'https://novelfire.net/book/the-perfect-run/chapter-' + chapterNumber; // Sử dụng window.location.href
                } else if (chapterNumber) {
                    alert('Chapter not found');
                }
            }

            $(".go-page").click(function(e) {
                const chapterNumber = parseInt($('.n-page').val());
                goToChapter(chapterNumber);
            });

            $(".n-page").on('keyup', function (e) {
                if (e.key === 'Enter' || e.keyCode === 13) {
                    const chapterNumber = parseInt($(this).val());
                    goToChapter(chapterNumber);
                }
            });
        </script>
        </body>
</html>
