
<!DOCTYPE html>
<html lang="en" theme="dark" bgcolor="black" hgcolor="purple">
    <head>
        <meta charset="utf-8"><title>The Perfect Run - Chapter 105: Miracle Cure - Novel Fire</title><meta name="description" content="Read Chapter 105: Miracle Cure - The Perfect Run online now!"><meta name="copyright" content="Copyright © Novel Fire"><meta name="author" content="Novel Fire"><meta name="robots" content="index,follow"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"><meta property="og:site_name" content="Novel Fire"><meta property="og:type" content="website"><meta property="og:url" content="http://novelfire.net/book/the-perfect-run/chapter-105"><meta property="og:title" content="The Perfect Run - Chapter 105: Miracle Cure - Novel Fire"><meta property="og:description" content="Read Chapter 105: Miracle Cure - The Perfect Run online now!"><meta property="og:image" content="https://novelfire.net/server-1/the-perfect-run.jpg"><meta property="og:locale" content="en_US"><meta name="twitter:card" content="summary"><meta name="twitter:url" content="http://novelfire.net/book/the-perfect-run/chapter-105"><meta name="twitter:title" content="The Perfect Run - Chapter 105: Miracle Cure - Novel Fire"><meta name="twitter:description" content="Read Chapter 105: Miracle Cure - The Perfect Run online now!"><meta name="twitter:image" content="https://novelfire.net/server-1/the-perfect-run.jpg"><meta name="twitter:creator" content="Novel Fire"><meta name="csrf-token" content="AQw0ZLnRKj4ttZy8jwAv5vu7MHYvEp1gwBoq81b7"><script type="text/javascript" nonce="219482a2af1c427f9b84b8066c6" src="//local.adguard.org?ts=1751941974845&amp;type=content-script&amp;dmn=novelfire.net&amp;url=https%3A%2F%2Fnovelfire.net%2Fbook%2Fthe-perfect-run%2Fchapter-105&amp;app=brave.exe&amp;css=3&amp;js=1&amp;rel=1&amp;rji=1&amp;sbe=1"></script>
<script type="text/javascript" nonce="219482a2af1c427f9b84b8066c6" src="//local.adguard.org?ts=1751941974845&amp;name=AdGuard%20Assistant&amp;name=AdGuard%20Extra&amp;name=AdGuard%20Popup%20Blocker&amp;type=user-script"></script><link rel="canonical" href="https://novelfire.net/book/the-perfect-run/chapter-105"><link rel="shortcut icon" href="https://novelfire.net/logo.ico?v=2"><link rel="apple-touch-icon" href="https://novelfire.net/apple-touch-icon.png?v=2"><link rel="preconnect" href="//fonts.gstatic.com" crossorigin><link rel="preconnect" href="//fonts.googleapis.com" crossorigin><link rel="preload stylesheet" as="style" href="https://fonts.googleapis.com/css?family=Roboto:400,500,600,700|Nunito+Sans:400,500,600,700&display=swap" crossorigin onload="this.rel='stylesheet'">
        <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:400,500,600,700|Nunito+Sans:400,500,600,700&display=swap"></noscript>
        <script type="application/ld+json">{ "@context":"https://schema.org/","@type" : "Organization", "name":"Novel Fire", "url":"https://novelfire.net", "slogan": "Novel Fire - Read Web Novels Online Free", "logo": "https://novelfire.net/logo.svg" }</script>
            <script type="application/ld+json">{ "@context":"https://schema.org/","@type" : "BreadcrumbList","itemListElement":[[{"@type":"ListItem","position":1,"name":"Novel","item":"https://novelfire.net/home"}],[{"@type":"ListItem","position":2,"name":"The Perfect Run","item":"https://novelfire.net/book/the-perfect-run"}],[{"@type":"ListItem","position":3,"name":"Chapter 105: Miracle Cure","item":"https://novelfire.net/book/the-perfect-run/chapter-105"}]] }</script>
        <link rel="stylesheet" type="text/css" href="https://novelfire.net/frontend/css/navbar.min.css?ver=1.2.21"><link rel="stylesheet" type="text/css" href="https://novelfire.net/frontend/css/media-mobile.min.css?ver=1.2.21"><link rel="stylesheet" type="text/css" media="screen and (min-width: 768px)" href="https://novelfire.net/frontend/css/media-768.min.css?ver=1.2.21"><link rel="stylesheet" type="text/css" media="screen and (min-width: 1024px)" href="https://novelfire.net/frontend/css/media-1024.min.css?ver=1.2.21"><link rel="stylesheet" type="text/css" media="screen and (min-width: 1270px)" href="https://novelfire.net/frontend/css/media-1270.min.css?ver=1.2.21"><link rel="stylesheet" type="text/css" href="https://novelfire.net/frontend/css/fontello.css?ver=1.2.21">
        <link rel="stylesheet" type="text/css" href="https://novelfire.net/frontend/css/chapterpg.min.css?ver=1.2.21" />        <link rel="stylesheet" href="https://novelfire.net/frontend/css/style.min.css?ver=1.2.21" type="text/css" media="all">
            </head>
    <body class="fade-out">
        <header class="main-header skiptranslate">
            <div class="wrapper">
                <div class="nav-logo"><a title="Novel Fire - Read Web Novels Online Free" href="https://novelfire.net/home"><img src="https://novelfire.net/logo.svg?v=2" alt="Novel Fire"></a></div>
                <div class="navigation-bar">
                    <nav><span class="lnw-slog fs-14">Your fictional stories hub.</span><ul class="navbar-menu"><li class="nav-item"><a title="Search Novels" href="https://novelfire.net/search" class="nav-link"><i class="icon-search"></i> Search</a></li><li class="nav-item"><a title="Explore The Recently Added Novels" href="https://novelfire.net/genre-all/sort-new/status-all/all-novel" class="nav-link"><i class="icon-th-large"></i> Browse</a></li><li class="nav-item"><a title="Novel Ranking" href="https://novelfire.net/ranking" class="nav-link"><i class="icon-diamond"></i> Ranking</a></li><li class="nav-item"><a title="Check out the recently added novel chapters" href="https://novelfire.net/latest-release-novels" class="nav-link"><i class="icon-book-open"></i> Updates</a></li><li class="nav-item"><a title="Member List" href="https://novelfire.net/user/member-list" class="nav-link"><i><svg xmlns="http://www.w3.org/2000/svg" width="16" height="20" fill="currentColor" viewBox="0 0 640 512"><path d="M96 224c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm448 0c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm32 32h-64c-17.6 0-33.5 7.1-45.1 18.6 40.3 22.1 68.9 62 75.1 109.4h66c17.7 0 32-14.3 32-32v-32c0-35.3-28.7-64-64-64zm-256 0c61.9 0 112-50.1 112-112S381.9 32 320 32 208 82.1 208 144s50.1 112 112 112zm76.8 32h-8.3c-20.8 10-43.9 16-68.5 16s-47.6-6-68.5-16h-8.3C179.6 288 128 339.6 128 403.2V432c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48v-28.8c0-63.6-51.6-115.2-115.2-115.2zm-223.7-13.4C161.5 263.1 145.6 256 128 256H64c-35.3 0-64 28.7-64 64v32c0 17.7 14.3 32 32 32h65.9c6.3-47.4 34.9-87.3 75.2-109.4z"/></svg></i> Members</a></li><li class="nav-item"><a title="Search Novels with Advanced Filtering Function" href="https://novelfire.net/search-adv" class="nav-link"><i class="icon-filter"></i> Filter</a></li><li class="nav-item"><a title="Development Announcements" href="https://novelfire.net/notices" class="nav-link"><i class="icon-megaphone"></i><span> DEV</span></a></li><li class="nav-item"><a class="nightmode_switch" data-tool="night" title="Dark Mode" data-night="0" data-content="Light Theme"><i class="icon-sun"></i></a></li></ul></nav>
                    <div class="login-wrap-nav">
                                                    <a class="nav-link login button" href="#modal" data-close-menu-mobile="1">Login</a>
                                            </div>
                </div>
                <div class="nav-back"></div><button id="mobile-menu-btn"><div id="burger-btn"></div></button><span class="nav notify-bell mobile-block icon-bell-alt"></span>
            </div>
        </header>
        <div class="sidebar-wrapper"></div><main role="main">    <div class="navbar-breadcrumb"><div class="breadcrumb show-dots container"><a title="Novel Fire - Read Web Novels Online Free" href="https://novelfire.net/home"><svg class="svg-home" height="20" viewBox="0 0 24 24" width="20"><path d="M0 0h24v24H0z" fill="none"/><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg> Novel</a> <svg width="10" height="10" fill="currentColor" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/></svg> <a title="The Perfect Run" href="https://novelfire.net/book/the-perfect-run">The Perfect Run</a> <svg width="10" height="10" fill="currentColor" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/></svg> <a title="Chapter 105: Miracle Cure" href="https://novelfire.net/book/the-perfect-run/chapter-105">Chapter 105: Miracle Cure</a></div></div>
    <svg aria-hidden="true" style="position:absolute;width:0;height:0;overflow:hidden"><symbol id="i-set" viewBox="0 0 1027 1024"><path d="M1005.37728 614.4l-115.2-64c0-12.8 0-25.6 0-38.4s0-25.6 0-38.4l115.2-64C1024.57728 403.2 1030.97728 384 1018.17728 364.8l-128-224c-6.4-12.8-25.6-19.2-44.8-12.8l-115.2 64c-19.2-12.8-38.4-25.6-64-38.4l0-128C672.57728 12.8 659.77728 0 640.57728 0L384.57728 0C365.37728 0 352.57728 12.8 352.57728 32l0 128c-19.2 12.8-44.8 19.2-64 38.4l-115.2-64C160.57728 121.6 141.37728 128 134.97728 147.2l-128 224C-5.82272 384 0.57728 403.2 13.37728 409.6l115.2 64C128.57728 486.4 128.57728 499.2 128.57728 512s0 25.6 0 38.4l-115.2 64C0.57728 620.8-5.82272 640 6.97728 659.2l128 224c6.4 12.8 25.6 19.2 44.8 12.8l115.2-64c19.2 12.8 38.4 25.6 64 38.4l0 128C352.57728 1011.2 365.37728 1024 384.57728 1024l256 0c19.2 0 32-12.8 32-32l0-128c19.2-12.8 44.8-19.2 64-38.4l115.2 64c12.8 6.4 32 6.4 44.8-12.8l128-224C1030.97728 640 1024.57728 620.8 1005.37728 614.4zM838.97728 774.4l-115.2-70.4c-38.4 44.8-89.6 70.4-147.2 83.2l0 134.4L448.57728 921.6l0-134.4c-57.6-12.8-108.8-44.8-147.2-83.2l-115.2 70.4-64-108.8 115.2-70.4C230.97728 569.6 224.57728 544 224.57728 512s6.4-57.6 12.8-83.2L122.17728 358.4l64-108.8 115.2 70.4C339.77728 275.2 390.97728 243.2 448.57728 230.4L448.57728 96l128 0 0 134.4c57.6 12.8 108.8 44.8 147.2 83.2l115.2-70.4 64 108.8-115.2 70.4c6.4 25.6 12.8 57.6 12.8 83.2s-6.4 57.6-12.8 83.2l115.2 70.4L838.97728 774.4z"></path><path d="M512.57728 320C403.77728 320 320.57728 403.2 320.57728 512s83.2 192 192 192 192-83.2 192-192S621.37728 320 512.57728 320zM512.57728 640c-70.4 0-128-57.6-128-128s57.6-128 128-128 128 57.6 128 128S582.97728 640 512.57728 640z"></path></symbol></svg>
    <article id="chapter-article" itemscope itemtype="https://schema.org/CreativeWorkSeries" itemid="https://novelfire.net/book/the-perfect-run/chapter-105">
        <section class="page-in content-wrap py-md-3 pt-0">
            <div class="titles"><meta itemprop="datePublished" content="2023-09-10 04:22:41"><link itemprop="image" href="https://novelfire.net/server-1/the-perfect-run.jpg"><meta itemprop="url" content="https://novelfire.net/book/the-perfect-run/chapter-105"><h1 itemprop="headline"><a title="The Perfect Run" href="https://novelfire.net/book/the-perfect-run" class="booktitle" rel="up" itemprop="sameAs">The Perfect Run</a><span hidden=""> - </span><br><span class="chapter-title">Chapter 105: Miracle Cure</span></h1><button id="control-action-btn" type="button"><svg><use xlink:href="#i-set"></use></svg></button></div>
            <div class="clearfix chapternav skiptranslate"><a title="Previous Chapter" rel="prev" class="button prevchap " href="https://novelfire.net/book/the-perfect-run/chapter-104"><i class="icon-left-open"></i></a><div><select class="button chapindex select2 wrap pointer" name="chapter_id"><option value="https://novelfire.net/book/the-perfect-run/chapter-105">Chapter 105: Miracle Cure</option></select></div><a title="Next Chapter" rel="next" class="button nextchap " href="https://novelfire.net/book/the-perfect-run/chapter-106"><i class="icon-right-open"></i></a></div>
            <div id="chapter-container" class="d-chapter-content font_default">
                <div id="pfvidad" class="banner mUEpZVPr PTjSKReb _E-eJS_r mb-3">
                    <div id="pf-4729-1"><script>window.pubfuturetag = window.pubfuturetag || [];window.pubfuturetag.push({unit: "649d5ee304b327003ffaf101", id: "pf-4729-1"})</script></div>
                    <ins class="adv-3cd55358da52fc2056a359b6af1dcbdf" data-sizes-desktop="300x50,320x100,320x50,360x100,360x50" data-sizes-mobile="300x50,320x100,320x50,360x100,360x50" data-notification="1"></ins>
                </div>
                <div id="content" class="clearfix" itemprop="description">
                    
               
                        <p>Bacchus returned to his factory to find it in ruins.</p><p>The face he made upon seeing his base burning would forever remain one of Ryan’s most cherished memories. Such a subtle mix of disbelief, anger, horror… the priest had clenched his jaw so tightly that the courier worried he might break his teeth.</p><p>Augustus’ reaction had been <em>far </em>less amusing though.</p><p>After landing on the island to see things for himself and hearing reports of the Carnival’s involvement, the mob boss had decimated a tenth of the surviving guards. Literally. He picked one out of ten at random, and had their peers beat them to death.</p><p>With their bare hands.</p><p>Though Ryan watched the whole show from the safety of Len’s underwater base, a spy bathysphere providing a living feed, the scene had sent chills down his spine.</p><p>The courier feared for Vulcan’s life in particular. She had been in charge of maintaining the defensive perimeter which the strike team easily dealt with, and Mob Zeus was clearly out for blood. The Genius was too precious to kill, but Augustus didn’t strike Ryan as the most rational person in the room.</p><p>“I kinda feel bad for these mooks,” Ryan said, as the video feed cut.</p><p>“They deserved it,” Len replied at his side, typing on her workshop’s computer. Servers thrummed next to steampunk-ish metal pipes, a song of steam and heat. “They were protecting a slaughterhouse, Riri.”</p><p>“I agree, but getting beaten to death by your own teammates is a ghastly way to die.” More to the point, it gave a taste of what Augustus would do to Ryan’s friends if he ever learned of their involvement. “Can you evacuate the base in short order if needed? I have the feeling Lightning Butt might pay it a visit in the near future.”</p><p>“I can move the habitats if I am forewarned,” his best friend replied. “They were, uh, designed to be self-sufficient. Each of them. If I disassemble them, they can move around independently.”</p><p>“Tiny islands of communism in a capitalistic sea… did you call it the Cuba Protocol?”</p><p>Len looked up from her computer, her beautiful eyes reeking of guilt.</p><p>“You did,” Ryan said, horrified.</p><p>“The Cuba Initiative,” she said, weakly.</p><p>Ryan studied his poor deluded accomplice, only for his eyes to wander to her clothes. While the time-traveler kept the Saturn armor on, Len had made herself comfortable in her lair; yet instead of her usual jumpsuit, she now wore a pair of blue overalls and a white shirt. The ensemble reminded the courier of these USSR worker ads.</p><p>“Riri?”</p><p>“Are these new clothes?” Ryan asked, never remembering them from the half a dozen or so loops they spent together.</p><p>“Yes.” She blushed a little, intimidated. “Alchemo’s treatment is good for my mood, and I… I thought I should try something else. Something brighter.”</p><p>“As long as you don’t wear red,” Ryan mused.</p><p>Len looked away. “I… I don’t want to do so. I’ve seen enough red for a lifetime.”</p><p>Ryan suddenly wondered if her obsession with communist iconography hadn’t been a subconscious attempt to keep her father’s memory alive. Though that sounded a bit too far-fetched and Freudian for him. “Have you pondered Bloodstream’s situation?” he asked. He knew what needed to be done, but she had to accept it.</p><p>“I did,” Len replied with a nod, her gaze harshening. “If… if the treatment we’re developing to cure Psychos doesn’t work on him…”</p><p>She took a long, heavy breath.</p><p>“If it doesn’t work,” Len said, with an air of finality. “I will do it myself.”</p><p>She would euthanize her father.</p><p>“Are you sure?” Ryan asked, mindful of her wellbeing. This would be a horrifying ordeal for anyone. “I can do it for you.”</p><p>“No, Riri. You already did a lot of things on my behalf. It’s… it’s my duty. My choice. He’s… my father’s memory. He deserves that much.”</p><p>“You will carry this pain all your life.”</p><p>“I know,” Len replied, her gaze determined. “I know. But I will still do it. I have to.”</p><p>It took a while, but she had finally decided to break out of her father’s shadow. To bury his ghost, if the man inside was gone for good.</p><p>Ryan put a hand on her shoulder in sympathy, making her smile sadly. “Thanks, Riri,” she said, putting her own hand over his own. He could have sworn he could sense the heat through the metal gauntlet. “I… I know I’m difficult. A wreck. Most wouldn’t have had your patience. Wouldn’t have stayed so long to help.”</p><p>“We’re both sinking ships,” Ryan said. “Gotta stick together, if we want to stay afloat.”</p><p>“We <em>were </em>sinking ships,” she replied, removing her hand. “We were.”</p><p>The workshop’s computer bleeped, as Len received a call from the Mechron bunker. The picture of Stitch appeared on the screen, with Mr. Wave petting Eugène-Henry in the background. “Greetings, sir,” the plague doctor said. “It has reached my ears that the raid was a success.”</p><p>“Mr. Wave likes this cozy place,” Mr. Wave said in the background, Eugène-Henry meowing his hands. “But Mr. Wave feels his portrait is missing somewhere.”</p><p>“I keep a poster in my room,” Ryan said. “I also have the Mr. Wave doll, the Mr. Wave ring, and the Mr. Wave hat.”</p><p>“The Mr. Wave-themed candy trucks too?”</p><p>“Now, I like you, but not as much as my car,” Ryan replied. That old machine had been at his side far longer than his Mr. Wave merchandise collection.</p><p>“Mr. Wave respects a man who loves his car,” the superhero said. “Mr. Wave apologizes for not visiting your underwater base. Mr. Wave doesn’t like being wet.”</p><p>“My teammate does not do well in underwater situations,” Stitch mused. “As our fight with the Kraken gang seven years ago can attest.”</p><p>“Mr. Wave doesn’t sink,” the Red Genome protested. “He <em>waits</em>. Like alligators.”</p><p>“In any case, the Bliss victims you sent me are getting medical attention as we speak,” Stitch said. “Alchemo is enthusiastic about repairing the brain damage they suffered, and early test results of our Bliss vaccine are promising. The Bloodstream cure also proved effective.”</p><p>Len typed on her keyboard, and video footage of Mosquito’s and Mongrel’s underwater habitats appeared on the screen. Knockoffs had turned each of them into Psychos in combination with a true Elixir, and both had received a dose of the vaccine.</p><p>Ryan almost didn’t recognize either of them.</p><p>Mongrel’s tumors had vanished, his disfigured face now smooth and unblemished. His bloodshot eyes had reverted to their natural brown color, and a black tuft of hair had started growing back on his head. He remained scrawny as ever, but his posture was straight, his clothes clean.</p><p>As for Mosquito, the courier wouldn’t have recognized him without the video feed indicating his cell number; for the insect had turned into a man. A small, pudgy man in his early forties, with brown skin and a beardless face. He looked vaguely Spanish, with the eyes of a veteran drinker.</p><p>It seemed that without a Psycho mutation, Mosquito could revert back and forth between his insectoid and human form.</p><p>Both had been granted a separate underwater habitat as a lair. Mongrel cut onions on his small kitchen counter, a bright smile on his face. He looked happy, like a cancer patient having made a miracle recovery. In contrast, Mosquito was reading a book and clearly bored out of his mind. Ryan couldn’t blame him, since Len’s library was limited to Karl Marx and Jules Verne.</p><p>They looked so... normal.</p><p>And most importantly, they had done it.</p><p>They had cured two Psychos.</p><p>“Did Sarin see this?” Ryan asked. Miss Gasshole was growing impatient about getting a treatment, especially since Alchemo hadn’t found a way to transfer her memories back. “We’re getting closer to a perfect cure.”</p><p>The plague doctor was suddenly far less enthusiastic. “Curing Psychos who gained their powers from Knockoffs is currently possible, since our vaccine destroys the Bloodstream particles in their circulatory system and reverses the genetic damage. As illustrated by Mosquito’s cases, the true Elixir takes over and turns them back into the Genomes they used to be. The problem is when a Psycho uses two true Elixirs.”</p><p>Len changed the video feed to that of Frank the Mad—or Vladimir the Russian, depending on who you asked. The metal giant spent his time glancing at the abyss beyond his porthole, dazed.</p><p>“We have no way of destroying an extra Elixir, if that is even possible,” Stitch explained. “I heard of a White Genome among the Augusti who could help, though.”</p><p>“Cancel only suppresses an Elixir’s connection to their colored dimension,” Ryan said. “She doesn’t destroy the Elixir itself, nor the mutations it causes.”</p><p>“What is the problem with the cure?” Len asked, confused.</p><p>“Unlike Knockoffs, Elixirs don’t use DNA to exchange information,” Stitch explained. “They can understand and modify it, like a painter and a canvas, but they work on a different level. If one of my plagues modifies a target’s DNA, even using the Neanderthal gene ratio you believe is the key to managing two powers at once, the Elixirs reassert their old patterns.”</p><p>“Unless we can inform the Elixirs of their ‘mistake’ in the bonding process, they will see any DNA modification of their host as an outside influence to be rejected,” Ryan guessed.</p><p>Stitch nodded. “My thoughts exactly. The problem is even greater with Psychos with abnormal, non-DNA-based biologies, such as your friend Sarin, Frank, or Gemini. Their bodies are made of gas, metal, or shadows, not flesh. My own power is clueless about how to deal with them.”</p><p>And this made memory transfer difficult. Their consciousness wasn’t hosted in a brain, to the point Alchemo struggled to transfer Sarin’s memories.</p><p>“Maybe the Elixirs use Flux to exchange information?” Ryan suggested. This was his best guess, from what he had learned so far.</p><p>“Perhaps,” Stitch conceded. “But again, this is beyond my expertise, let alone my peers Alchemo and Dr. Tyrano. Mechron’s database has a wealth of information on the matter, but nowhere near enough. It took its AIs years to create imperfect Knockoffs. They might need a decade to fully reverse-engineer the original potions.”</p><p>“We need more information on the bonding process,” Ryan said. “I know a place where we could learn more about Elixirs.”</p><p>“If this is true, I would like to accompany you,” the plague doctor said with eagerness. “I was skeptical at first about this project, but now I believe we are close to a great discovery. If we can negate the dangerous side-effects of Elixirs, then we could help society recover.”</p><p>The courier saw no reason to deny him. “Sure, but you should trade your gloves for mittens.”</p><p>“Mr. Wave will provide the warmth, he’s a one-man global warming,” Mr. Wave said in the background, as Len stopped the communication.</p><p>Ryan crossed his arms, considered his options, and then looked at his best friend. “Any way you can help with this, Shortie?”</p><p>“It’s not my specialty,” Len replied in the negative. “I pushed my power’s limits with the transfer machine, but Elixir communications… that’s beyond me, Riri.”</p><p>“Then let’s prepare for our winter vacations,” Ryan said. “Can you outfit the submarine for the trip?”</p><p>“Yes, of course,” she said with a nod. She looked rather eager to leave New Rome. “Do we have time to stop at the Canary Islands on the way? The children will love it.”</p><p>“Sure,” Ryan replied with a proud smile. A few loops ago, Len had considered abandoning the surface altogether. She would have asked to go to Antarctica without delay, refusing to take a moment to breathe and simply enjoy the wonders on the way.</p><p>It had taken a lot of effort, but she was starting to live again.</p><p>When twilight came, Ryan picked up Livia at Mount Augustus by car, and moved to Mars and Venus’ home.</p><p>Unlike their daughter’s apartment, the Augusti power couple lived in an English-styled manor close to Mount Augustus. The mansion was three stories tall, and made of grey stone and stained-glass windows rather than marble. Ryan found it a nice change from Augustus’ obsession with antiquity and Roman aestheticism.</p><p>The place remained luxurious though, with a fountain in the garden and doormen in fancy clothes awaiting guests. Ryan noticed a few vehicles near the entrance, including Jamie’s and Cancel’s.</p><p>With the Bliss Factory’s destruction, the Augusti had decided to increase security around Narcinia, and Lightning Butt's daughter too. When Ryan came to pick his girlfriend, Mortimer and Sparrow had escorted them all the way to their destination on motorcycles. For a moment, the time traveler felt like a president again, with bodyguards ready to die for his safety. Or at least his First Lady’s.</p><p>They didn’t suspect him of treachery though. Ryan had sent Incognito, a Meta-Gang member, to act as his decoy. By using his powers to pose as the courier, the Psycho had provided him with a foolproof alibi. As far as the Augusti were concerned, Ryan had gambled colossal sums of money at their casinos while the Bliss Factory burnt. And all it cost was a shot of Knockoff Elixirs to satisfy Incognito’s addiction.</p><p>Also, Ryan had infected him with nanites that would explode in his blood if he misbehaved. It was Mechron’s version of an ankle tag, and quite effective.</p><p>The courier had traded his Saturn armor for a purple shirt and black pants, simple yet elegant. Meanwhile, Livia had chosen to wear a black turtleneck and pants, as if she went to a funeral rather than a dinner. Which in this case wasn’t that far from the truth.</p><p>Ryan’s girlfriend hadn’t said a word during the trip.</p><p>“Are you sulking?” the courier asked, as he parked his car near the fountain. Sparrow and Mortimer did the same with their motorcycles. The former stayed near the Plymouth Fury, and the latter exchanged words with the doorsmen to check up on the security perimeter.</p><p>Livia brought a mobile phone out of her pocket and wordlessly showed Ryan the screen.</p><p>It was a picture her boyfriend sent her after destroying the Bliss Factory. The courier posed in front of the fortress’ smoking rubble, thumb raised, while Mr. Wave peeked from the right. Ryan had drawn words in the sand, as a message to Lightning Butt.</p><p><em>“Hey Auggy, still looking for that lightning rod!”</em></p><p>“That was terrible,” Livia said, as she switched out her phone. “If we were married, it would be grounds for divorce.”</p><p>“The joke struck you as bad?”</p><p>“Yes, it d—” She paused, before rolling her eyes. “Struck, truly?”</p><p>“I was just as shocked when the idea crossed my mind.” This time it made her giggle. “I guess I didn’t spend enough time brain<em>storming</em>.”</p><p>“When it rains, it pours,” Livia lamented, a smile on her face.</p><p>“Aw come on, I can see you like my puns.”</p><p>“They are bad, Ryan,” she said, wounding his sensitive heart, “but so bad they end up being funny anyway.”</p><p>Ryan looked at the building. “So what’s the plan to get Narcinia out of her parents’ claws? They won’t let her out of sight after the loss of their drug lab.”</p><p>“I have an idea in mind, but I need a little more time to refine it,” Livia said. “Whenever your actions have a large impact, it takes me a while to see the ripples you make.”</p><p>Her boyfriend’s hands tightened on the driving wheel. “Bacchus kept test subjects in the factory’s basement,” he informed her. “One of them died.”</p><p>“I saw the possibility,” she said, looking away. “I hoped I was wrong.”</p><p>“Will the victim be alive a few days ago?” Ryan asked, his girlfriend nodding hesitantly. “Then I will strike the factory as soon as the Meta-Gang is dealt with in the next loop.”</p><p>“Narcinia and Bacchus will be inside,” she warned. “It will be far harder.”</p><p>“I will manage.” Now that he could destroy Geist and had access to the Bliss Factory’s systems, he could easily shatter the defenses in his next run. “I could take Narcinia to the Carnival while I’m at it.”</p><p>“My father will react badly.”</p><p>“I won’t give him the time.”</p><p>Livia joined her hands, and locked eyes with her boyfriend. “You intend to fight him,” she said, her voice breaking.</p><p>“Yes.”</p><p>Ryan hadn’t dared to breach the subject openly, but… he couldn’t sugarcoat the truth. Even though it risked damaging their relationship, Shroud had a point. The courier liked Livia, but not to the point of letting her father get away with his crimes. He had to be stopped for the good of everyone else.</p><p>“Livia, your father knew about Bacchus’ prisoners,” Ryan said. “We saw that in the factory’s terminal. He did worse than give his consent, he sent his pet priest more victims to refine his product. He murdered Narcinia’s parents and thousands more. Hell, he chose to settle an old score rather than help when Bloodstream threatened to turn everyone into tomato juice. That shows how little he cares about life. Your dad won’t stop killing, unless he’s forced to.”</p><p>“I know!” Her voice broke, the seer closing her eyes and gathering her breath. “I know that, Ryan. I know what he is. But he has little time already. I have seen it. Can’t… can’t you just wait? If we delay enough—”</p><p>“When he senses his death approaching, Lightning Butt will go on a rampage and slaughter countless more.” Ryan remained silent for a few seconds, letting his girlfriend recover. “Livia, you can’t talk him out of his madness.”</p><p>“We can,” she said. “With you at my side, I can find it.”</p><p>“Livia, I will help as much as I can,” Ryan promised, “but I don’t think words will work with your dad. Not even yours. If the apocalypse couldn’t make him pause, nothing will.”</p><p>“Then what am I supposed to do?” she asked, holding back tears. “Give you my permission to kill my own father? Is that what you want?”</p><p>“I don’t have to kill your father to neutralize him.”</p><p>“Then how? Dynamis and the Carnival tried everything. Nothing worked. Nothing they have will work. I’ve seen where it leads.” Her fingers fidgeted on her lap. “You killed Geist. Somehow.”</p><p>“I did.”</p><p>“You have something that could kill my father. Or you think it could.”</p><p>“Yes,” Ryan admitted.</p><p>Livia bit her lower lip, the same way Len did. “Why are you telling me this, Ryan?”</p><p>“Because I trust you.”</p><p>There, he said it.</p><p>“I trust you.” Ryan took her hands into his own, making her blush slightly. “I want you at my side. I want to stay with you even after we clean this city up. I want our relationship to work. I don’t want something built on lies. I’ve seen where it led with Safelite and his golden retriever.”</p><p>“That’s a mean way to call my best friend,” Livia replied, her fingers tightening around his own. “I want us to work too, Ryan… but I don’t want my boyfriend and my father to kill each other. I don’t want to see that. I almost did when he caught you, and… I don’t think I can shoulder it. Even if you reverse time afterward.</p><p>“I… I think I can imprison your dad,” the courier said. “Where no one will break him out.”</p><p>“You will send him to Monaco, or a pocket dimension?” Livia shook her head. “He will break out. He has Geniuses on his payrolls, loyal lieutenants, contingencies.”</p><p>“He won’t have that for long.”</p><p>“You can’t destroy an entire organization by yourself.”</p><p>“I can and I did,” Ryan replied. “And I’m not alone this time.”</p><p>“Ryan, I know there is a solution. A peaceful solution.” Livia’s hands broke away from his own. “I’m… I’m okay with imprisonment, or forced retirement. But not death. I’m sick of all these murders and violence. It has to cease somewhere.”</p><p>“You’re okay with killing Bacchus,” Ryan pointed out, “but not the one giving him his orders?”</p><p>“Bacchus is not family,” Livia replied, fidgeting in her seat. “That’s selfish, I know. I won’t deny it. I want to save people, Ryan, but I don’t want to see my family perish either. I don’t want to make that choice. If you had to kill a loved one to save the world, would you do it?”</p><p>“I would find a third option.”</p><p>“My point exactly. You said you could do anything if given time, and… and I hope we can find a third option too.”</p><p>Ryan said nothing for a while, as Livia gathered her thoughts. She knew Augustus had to be stopped, and on some level, she understood there wasn’t a way to do so peacefully. He could see it on her face.</p><p>In the end, she opened her mouth again, her voice a whisper, “Promise me, Ryan.”</p><p>“Promise you what?”</p><p>“That you won’t kill my father,” Livia said, her face grave and heavy. “Don’t kill him, please. Or we are done. Drag him off his throne, destroy his kingdom of sin, imprison him underwater until he expires, I… I can live with that. But don’t kill him. Please.”</p><p>For a tense half a minute, they locked eyes with each other.</p><p>If Ryan refused, he would make an enemy of Livia. She cared for her family, as much as he loved his own friends. Even if he survived the ensuing retribution, whatever future they could build together would die with Lightning Butt. They would remain bitter enemies.</p><p>Augustus deserved death for his crimes. He might not have been as bad as Big Fat Adam, but his crimes were almost as horrendous. He had murdered his way across the world for years, and he remained unrepentant.</p><p>And yet… Ryan had lived long enough to know there were punishments far more terrible than death. It was all a question of imagination.</p><p>“I won’t kill your father,” Ryan swore, to Livia’s relief, “but I will defeat him. <em>Decisively</em>. I will break him so thoroughly that he will never threaten anyone else again.”</p><p>The Black Ultimate One had granted him the power to harm Augustus, and the time-traveler would make use of it.</p><p>His girlfriend couldn’t help but respond with nervous laughter. “Your last encounter wasn’t glorious.”</p><p>“That’s the good thing about me, princess. I can lose a thousand times, but only have to win once.” Ryan looked back at the manor. “I won’t kill him, for you. But I will rough him up, and he won’t escape karma. He has too much blood on his hands, and his victims deserve justice. His punishment won’t be lethal, but it won’t be merciful either. Are you alright with that?”</p><p>“It’s a compromise,” she replied, shaking her head. “None of us get all that we want.”</p><p>Perhaps. But it was better than getting nothing.</p>
                                    </div>
                                    <div class="d-pc-hidden d-mobile-block box-notification text-center mb-3"><div>Do you like this site? Donate here:</div><div style="height:50px" class="my-1"><a title="Donate" target="_blank" href="https://ko-fi.com/lnworld"><img src="https://novelfire.net/bmc-button.png" style="width:auto;height:50px"></a></div><div>Your support helps us create quick and quality chapters!</div></div>
                    <ins class="adv-3cd55358da52fc2056a359b6af1dcbdf" data-sizes-desktop="1050x300,200x200,250x250,300x250,336x280,400x350,468x60,678x60,700x300,728x500,728x90,750x400,750x480,870x200,970x250,970x90" data-sizes-mobile="200x200,250x250,300x250,300x50,320x100,320x50,360x100,360x50" data-interstitial="1"></ins>
                                <div class="clearfix text-center">
                    <ins class="adv-3cd55358da52fc2056a359b6af1dcbdf" data-sizes-desktop="200x200,250x250,300x250,336x280" data-sizes-mobile="200x200,250x250,300x250,336x280" data-slot="4"></ins>
                                            <div id="pf-7912-1"><script>window.pubfuturetag = window.pubfuturetag || [];window.pubfuturetag.push({unit: "65dffd73da20dc57bdfd8ee5", id: "pf-7912-1"})</script></div>
                                    </div>
                <div class="chapternav skiptranslate py-3 clearfix"><a title="Previous Chapter" rel="prev" class="button prevchap " href="https://novelfire.net/book/the-perfect-run/chapter-104"><i class="icon-left-open"></i></a><div><select class="button chapindex select2 wrap pointer" name="chapter_id"><option value="https://novelfire.net/book/the-perfect-run/chapter-105">Chapter 105: Miracle Cure</option></select></div><a title="Next Chapter" rel="next" class="button nextchap " href="https://novelfire.net/book/the-perfect-run/chapter-106"><i class="icon-right-open"></i></a></div>
                <div class="text-center box-notice"><span class="d-pc-block d-mobile-hidden">Tip: You can use left, right keyboard keys to browse between chapters.</span><span class="d-pc-hidden d-mobile-block">Tap the middle of the screen to reveal Reading Options.</span></div>
                <div class="report-container mt-3"><p>If you find any errors (non-standard content, ads redirect, broken links, etc..), Please let us know so we can fix it as soon as possible.</p><a id="novel-report" href="javascript:;" report-post_id="4951" report-chapter_id="59907671"><svg class="icon icon-pantool"><use xlink:href="#icon-pantool"></use></svg><span>Report</span></a></div>
                <div class="d-pc-hidden d-mobile-block box-notification mt-3 py-3">Follow our Telegram channel at <a href="https://t.me/novelfire" target="_blank">https://t.me/novelfire</a> to receive the latest notifications about daily updated chapters.
</div>
            </div>
            <dialog class="mobile-title-bar"><div class="bar-body"><i class="bar-nav-back"><svg viewBox="0 0 24 24" fill="none" width="30" height="30"><path d="M6.975 13.3L12 20H9l-6-8 6-8h3l-5.025 6.7H21v2.6H6.975z"></path></svg></i><div class="bar-titles"><a title="The Perfect Run" href="https://novelfire.net/book/the-perfect-run" class="booktitle text1row">The Perfect Run</a></div></div></dialog>
            <dialog class="control-action" translate="no"><nav class="action-items"><div class="head-bar"><div class="title">Chapter 105: Miracle Cure</div><button class="action-close" type="button"><i class="icon-cancel-1"></i></button></div><div class="action-select"><a rel="prev" class=" chnav prev" href="https://novelfire.net/book/the-perfect-run/chapter-104"><i class="icon-left-open"></i></a><a class="chap-index" title="Chapter Index" href="https://novelfire.net/book/the-perfect-run/chapters"><i class="icon-home"></i></a><a class="nightmode_switch" data-tool="night" title="Dark Mode" data-night="0" data-content="Light Theme"><i class="icon-sun"></i></a><a rel="next" class=" chnav next" href="https://novelfire.net/book/the-perfect-run/chapter-106"><i class="icon-right-open"></i></a></div><div class="font-select"><div class="font-wrap"><input type="radio" id="radioDefault" name="radioFont" value="default" checked=""><label for="radioDefault">Default</label><input type="radio" id="radioDyslexic" name="radioFont" value="dyslexic"><label for="radioDyslexic">Dyslexic</label><input type="radio" id="radioRoboto" name="radioFont" value="roboto"><label for="radioRoboto">Roboto</label><input type="radio" id="radioLora" name="radioFont" value="lora"><label for="radioLora">Lora</label></div></div><div class="action-select range-slider"><span class="svgbtn pointer" id="svgFontMinus"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M14.333 21l-1.703-4.6H5.37L3.667 21H1L7.667 3h2.666L17 21h-2.667zM9 6.6l2.74 7.4H6.26L9 6.6zM23 5h-8v2h8V5z" fill="#000"></path></svg></span><div class="range-fontsize"><div class="range"><input type="range" min="1" max="8" step="1" value="1"></div><ul class="range-labels"><li>14</li><li>16</li><li class="active selected">18</li><li>20</li><li>22</li><li>24</li><li>26</li><li>28</li></ul></div><span class="svgbtn pointer" id="svgFontPlus"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M20 2v3h3v2h-3v3h-2V7h-3V5h3V2h2zm-5.667 19l-1.703-4.6H5.37L3.667 21H1L7.667 3h2.666L17 21h-2.667zM9 6.6l2.74 7.4H6.26L9 6.6z" fill="#000"></path></svg></span></div><div class="lang-select"><select id="langselector" class="language-combo w-100"><option value="en" selected="selected">English</option><option value="ar">Arabic</option><option value="zh-CN">Chinese (Simplified)</option><option value="da">Danish</option><option value="nl">Dutch</option><option value="tl">Filipino</option><option value="fr">French</option><option value="de">German</option><option value="hi">Hindi</option><option value="hu">Hungarian</option><option value="id">Indonesian</option><option value="it">Italian</option><option value="ja">Japanese</option><option value="ko">Korean</option><option value="pl">Polish</option><option value="pt">Portuguese</option><option value="ru">Russian</option><option value="es">Spanish</option><option value="sv">Swedish</option><option value="th">Thai</option><option value="tr">Turkish</option><option value="uk">Ukrainian</option><option value="vi">Vietnamese</option></select><a id="langselbtn" href="javascript:;">Select Lang</a></div><div id="ttspanel"><button id="ttsstart"><i class="icon-play"></i></button><button disabled="" id="ttsstop"><i class="icon-stop"></i></button><select id="tts-rate" name="tts-rate"></select><div class="select-wrap"><select id="tts-voices" name="voices"></select></div></div></nav></dialog>
        </section>
        <div id="chapter-comments" class="content-wrap pt-3 pb-1 py-lg-0"></div>
    </article>
    </main>
        <footer><div class="wrapper skiptranslate"><div class="w-100"><div class="col-ft-1"><div class="logo text-center"><a title="Novel Fire - Read Web Novels Online Free" href="https://novelfire.net/home" style="display:inline-block"><img class="lazy footer-logo" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="https://novelfire.net/logo.svg?v=5" alt="Novel Fire"></a></div></div><div class="col-ft-4 mt-0 mt-lg-3 mb-3 mb-lg-0"><div><p>Novel Fire is created in hopes that every novel fan can get to read novel without paying a dime, this site is completely free of charge. To read novel online for free, all you need to do is to visit Novel Fire, search for the novel you want to watch, and enjoy reading it at no cost and with no risk.</div></div><div class="col-ft-2 mb-3 mb-lg-0"><h5>Userful links</h5><nav class="links"><ul><li><a title="Explore the Top Rated Novels" href="https://novelfire.net/ranking">Novel Ranking</a></li><li><a title="Explore The Recently Added Novels" href="https://novelfire.net/genre-all/sort-new/status-all/all-novel">Latest Novels</a></li><li><a title="Recently Added Novel Chapters" href="https://novelfire.net/latest-release-novels">Latest Chapters</a></li><li><a title="Completed Novels" href="https://novelfire.net/genre-all/sort-popular/status-completed/all-novel">Completed Novels</a></li><li><a title="Explore All Novel Tags" href="https://novelfire.net/all-tags/A">All Tags</a></li></ul></nav></div><div class="col-ft-2 mb-3 mb-lg-0"><h5>Page</h5><nav class="links"><ul><li><a title="Privacy Policy" href="https://novelfire.net/page/privacy-policy">Privacy Policy</a></li><li><a title="Terms of Service" href="https://novelfire.net/page/terms-of-service">Terms of Service</a></li><li><a title="Contact Us" href="https://novelfire.net/contact-us">Contact Us</a></li></ul></nav></div></div><div class="clearfix copyright py-2 w-100 text-center"><p>Made with ♥ for Novel Lovers</p><p class="fs-14">Disclaimer: This site Novel Fire does not store any files on its server. All contents are provided by non-affiliated third parties.</p></div></div></footer><script src="https://cdnjs.cloudflare.com/ajax/libs/js-cookie/3.0.1/js.cookie.min.js" integrity="sha512-wT7uPE7tOP6w4o28u1DN775jYjHQApdBnib5Pho4RB0Pgd9y7eSkAV1BTqQydupYDB9GBhTcQQzyNMPMV3cAew==" crossorigin="anonymous" referrerpolicy="no-referrer"></script><script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js" integrity="sha512-bLT0Qm9VnAYZDflyKcBaQ2gg0hSYNQrJ8RilYldYQ1FxQYoCLtUjuuRuZo+fjqhx/qtq/1itJ0C2ejDxltZVFg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script><script src="https://novelfire.net/frontend/js/appsettings.min.js?ver=1.2.21"></script><script src="https://novelfire.net/frontend/js/app.min.js?ver=1.2.21"></script><div class="ajax_waiting"></div><a id="back-to-top" href="#"><svg width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg" class="bi bi-arrow-up-short" style="color: rgb(255, 255, 255);"><path fill-rule="evenodd" d="M8 12a.5.5 0 0 0 .5-.5V5.707l2.146 2.147a.5.5 0 0 0 .708-.708l-3-3a.5.5 0 0 0-.708 0l-3 3a.5.5 0 1 0 .708.708L7.5 5.707V11.5a.5.5 0 0 0 .5.5z"></path></svg></a><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="position: absolute; width: 0; height: 0" id="__SVG_SPRITE_NODE__"><symbol id="i-times" viewBox="0 0 1024 1024"><path d="M618.775 512l320.329-320.329c30.51-30.51 30.51-76.269 0-106.775s-76.269-30.51-106.775 0l-320.329 320.329-320.329-320.329c-30.51-30.51-76.269-30.51-106.775 0s-30.51 76.269 0 106.775l320.329 320.329-320.329 320.329c-30.51 30.51-30.51 76.269 0 106.775s76.269 30.51 106.775 0l320.329-320.329 320.329 320.329c30.51 30.51 76.269 30.51 106.775 0s30.51-76.269 0-106.775l-320.329-320.329z"></path></symbol><symbol id="icon-pantool" viewBox="0 0 24 24"><path d="M21.5 4c-.83 0-1.5.67-1.5 1.5v5c0 .28-.22.5-.5.5s-.5-.22-.5-.5v-8c0-.83-.67-1.5-1.5-1.5S16 1.67 16 2.5v8c0 .28-.22.5-.5.5s-.5-.22-.5-.5v-9c0-.83-.67-1.5-1.5-1.5S12 .67 12 1.5v8.99c0 .28-.22.5-.5.5s-.5-.22-.5-.5V4.5c0-.83-.67-1.5-1.5-1.5S8 3.67 8 4.5v11.41l-4.12-2.35c-.58-.33-1.3-.24-1.78.22-.6.58-.62 1.54-.03 2.13l6.78 6.89c.75.77 1.77 1.2 2.85 1.2H19c2.21 0 4-1.79 4-4V5.5c0-.83-.67-1.5-1.5-1.5z"></path></symbol></svg>
    <div id="modal" class="popupContainer" style="display:none"><header class="popupHeader"><span class="header_title">Login</span><span class="modal_close"><svg><use xlink:href="#i-times"></use></svg></span></header><section class="popupBody"><div class="social_login"><div class="notification"></div><div><a href="https://novelfire.net/auth/redirect/google" class="social_box google"><span class="icon"><i class="i-googlePlus"></i></span><span class="icon_title">LOG IN WITH GOOGLE</span></a><a href="javascript:void(0)" id="login_form" class="social_box"><span class="icon"><i class="i-mail"></i></span><span class="icon_title">LOG IN WITH EMAIL</span></a></div><div class="centeredText">Don't have an account?<br><a href="javascript:void(0)" id="register_form" class="font-weight">Sign up with your email address.</a></div></div><div class="user_login edit-form"><form><label>Email</label><input type="email"><div class="alert alert-email"></div><label>Password</label><input type="password"><div class="alert alert-password"></div><div class="checkbox" style="display:flex"><input id="remember" type="checkbox"><label for="remember">Remember Me</label><label style="margin-left:auto"><a href="javascript:void(0)" id="forgot_password" class="forgot_password">Forgot Password?</a></label></div><div class="action_btns"><div class="one_half"><a href="javascript:void(0)" class="button btn-modal back_btn"><i class="icon-left-big"></i> Back</a></div><div class="one_half last"><a href="javascript:void(0)" class="button btn-modal" onclick="loginAjax()">Login</a></div></div></form></div><div class="user_register edit-form"><form><label>Email</label><input type="email"><div class="alert alert-email"></div><label>Username</label><input type="text"><div class="alert alert-name"></div><label>Password</label><input type="password"><div class="alert alert-password"></div><label>Confirm Password</label><input type="password" name="confirm-password"><div class="alert alert-confirm-password"></div><div class="action_btns"><div class="one_half"><a href="javascript:void(0)" class="button btn-modal back_btn"><i class="icon-left-big"></i> Back</a></div><div class="one_half last"><a href="javascript:void(0)" class="button btn-modal" onclick="registerAjax()">Sign Up</a></div></div></form></div><div class="user_forgot_password edit-form"><form><div class="pb-2"><p style="padding-bottom:10px">Enter your email address that you used to register. We'll send you an email with a link to reset your password.</p><p><i>If you don’t see the email, check other places it might be, like your junk, spam, social, or other folders.</i></p></div><input type="email" placeholder="Email"><div class="alert alert-email"></div><div class="action_btns"><div class="one_half"><a href="javascript:void(0)" class="button btn-modal back_btn_from_forgot_password"><i class="icon-left-big"></i> Back</a></div><div class="one_half last"><a href="javascript:void(0)" class="button btn-modal" onclick="codePasswordResetAjax()">Send</a></div></div></form></div><div class="form_alert"><form><div class="alert"></div></form></div></section></div>
    <script src="https://novelfire.net/frontend/js/modal.min.js?ver=1.2.21"></script>
 <script>window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}gtag('js', new Date());gtag('config', 'G-9YW3E218EG');</script>
         <script type="text/javascript">(function(a) { if (!document.getElementById(a)) { const s = document.createElement("script"); s.id = a; s.async = true; s.src = ["https://fstatic.netpub.media/static/", a, ".min.js?", Date.now()].join(""); document.head.appendChild(s); } })("3cd55358da52fc2056a359b6af1dcbdf");</script><script async src="https://novelfire.net/frontend/js/chaptertts.min.js?ver=1.2.21"></script><script src="https://novelfire.net/frontend/js/chap-google-translate.min.js?ver=1.2.21"></script><script data-ignore="true" async type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=lnclient.chapgoogletranslate.translateCallback"></script>
        <script>
            $("#content p:nth-child(15)").after('<div class="box-ads py-1"><div id="bg-ssp-9629"><script data-cfasync="false">var adx_id_9629 = document.getElementById("bg-ssp-9629");adx_id_9629.id = "bg-ssp-9629-" + Math.floor(Math.random() * Date.now());window.pubadxtag = window.pubadxtag || [];window.pubadxtag.push({zoneid: 9629, id: adx_id_9629.id, wu: window.location.href})<\/script></div></div>');
            $("#content p:nth-child(30)").after('<div class="box-ads py-1"><div id="pf-7911-1"><script>window.pubfuturetag = window.pubfuturetag || [];window.pubfuturetag.push({unit: "65dff96bda20dc57bdfd8a8b", id: "pf-7911-1"})<\/script></div></div>');
            document.addEventListener("DOMContentLoaded",function(event){
                setTimeout(function(){var script = document.createElement('script');script.type = 'text/javascript';script.async = true;script.src = "//platform.pubadx.one/pubadx-ad.js";document.head.appendChild(script);},10);
            });
            chapter_id = parseInt("59907671");post_id = parseInt("4951");load_comment_first = false;
            scrollToElementOnPage('#chapter-comments');
            $(window).scroll(function() {
                if (load_comment_first == false) {
                    var hT = $('#chapter-comments').offset().top,
                        hH = $('#chapter-comments').outerHeight(),
                        wH = $(window).height(),
                        wS = $(this).scrollTop();
                    if (wS > (hT+hH-wH)){
                        load_comment_first = true;
                        $('<link/>', {rel: "stylesheet", type: "text/css", href: "https://novelfire.net/frontend/css/comments.min.css?ver=1.2.21"}).appendTo('head');
                        loadFormComment();
                        $.when(
                            $.getScript("https://novelfire.net/frontend/js/comment.min.js?ver=1.2.21"),
                            $.Deferred(function( deferred ){
                                $( deferred.resolve );
                            })
                        ).done(function(){
                            showComment();
                        });
                    }
                }
            });
            $('select').on('select2:open', function(){$('.select2-search__field').attr('placeholder', 'Search...');var height_screen = $(window).height();var height_criterion = height_screen - height_screen*(35/100);$('.select2-results__options').css('max-height', parseInt(height_criterion - 50) + 'px')});
            $("select[name=chapter_id]").click(function(){var _self = $(this);var n_option = $(_self).children('option').length;if (n_option == 1){$(_self).html("<option>loading...</option>");$('<link/>', {rel: "stylesheet", type: "text/css", href: "https://novelfire.net/frontend/css/select2.min.css?ver=1.2.21"}).appendTo('head');$.when($.getScript("https://novelfire.net/frontend/js/select2.min.js?ver=1.2.21"),$.ajax({method: "POST",url: 'https://novelfire.net/ajax/getListChapterById',data: { post_id: post_id, _token: $('meta[name="csrf-token"]').attr('content') },dataType: 'json'})).done(function(script, response) {var results = response[0].data;$(_self).html('');$.each(results, function(i, item) {$(_self).append('<option value="'+ item.id +'" data-n_sort="'+ item.n_sort +'">'+ item.title +'</option>');});$(_self).select2({ width: '100%', dropdownCssClass: 'bigdrop' });$(_self).val(chapter_id).trigger('change');$(_self).select2('open');$('.select2-search input').prop('focus', true);$(_self).blur();});}});
            $("select[name=chapter_id]").change(function(){if ($(this).find(":selected").val() != chapter_id) {var n_sort = $(this).find(":selected").data('n_sort');location.href = 'https://novelfire.net/book/the-perfect-run/chapter-' + n_sort;}})
            $.ajax({method: "POST",url: 'https://novelfire.net/ajax/countVisitedAndSaveHistory',data: {post_id:post_id,chapter_id:chapter_id,_token:$('meta[name="csrf-token"]').attr('content')},dataType: 'json',success: function (response) {if (response.login == false && typeof Cookies.get('notification-login') === 'undefined'){setTimeout(function(){$('.social_login .notification').html('<div class="alert alert-info fade in show">By logging into the system, you can subscribe to the novel you want to follow and start receiving update notifications. We wish you a pleasant reading.</div>');$('a.login').trigger('click');Cookies.set('notification-login', '1', { expires: 0.5 })}, 20000);}}});
            //Remove slug url tmp
            var urlCurrent = window.location.href;var questionMarkIndex = urlCurrent.indexOf('?ac');if(questionMarkIndex !== -1){var newUrl = urlCurrent.substring(0, questionMarkIndex);window.history.replaceState({}, document.title, newUrl);}
        </script>
        <script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'95bcd474df5640f7',t:'MTc1MTk0ODg1NS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
