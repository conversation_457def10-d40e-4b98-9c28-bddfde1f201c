[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "wn-dl"
version = "0.1.0"
description = "A modular web novel scraper with EPUB generation"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "wongpinter", email = "<EMAIL>"}
]
maintainers = [
    {name = "wongpinter", email = "<EMAIL>"}
]
keywords = [
    "web-scraping",
    "novels",
    "epub",
    "ebooks",
    "scraper",
    "novelbin"
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: Browsers",
    "Topic :: Text Processing :: Markup :: HTML",
    "Topic :: Multimedia :: Graphics :: Graphics Conversion",
]
requires-python = ">=3.8"
dependencies = [
    "aiohttp>=3.8.0",
    "beautifulsoup4>=4.11.0",
    "lxml>=4.9.0",
    "pillow>=9.0.0",
    "pyyaml>=6.0",
    "cloudscraper>=1.2.60",
    "rich>=12.0.0",
    "click>=8.0.0",
    "aiofiles>=22.0.0",
    "asyncio-throttle>=1.0.0",
    "ebooklib>=0.18",
    "markdown>=3.4.0",
    "psutil>=7.0.0",
    "markdownify>=0.11.6",
    "sqlalchemy>=2.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
    "isort>=5.10.0",
    "pre-commit>=2.20.0",
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0",
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "myst-parser>=0.18.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "responses>=0.22.0",
    "aioresponses>=0.7.0",
]

[project.urls]
Homepage = "https://github.com/wongpinter/webnovel-scraper"
Documentation = "https://github.com/wongpinter/webnovel-scraper/tree/main/docs"
Repository = "https://github.com/wongpinter/webnovel-scraper"
"Bug Tracker" = "https://github.com/wongpinter/webnovel-scraper/issues"
Changelog = "https://github.com/wongpinter/webnovel-scraper/blob/main/CHANGELOG.md"

[project.scripts]
wn-dl = "wn_dl.cli:main"

[tool.hatch.build.targets.wheel]
packages = ["src/wn_dl"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/tests",
    "/docs",
    "/config",
    "/examples",
    "README.md",
    "LICENSE",
    "CHANGELOG.md",
]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["wn_dl"]

# mypy configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "cloudscraper.*",
    "bs4.*",
    "lxml.*",
    "PIL.*",
]
ignore_missing_imports = true

# pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=wn_dl",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "network: marks tests that require network access",
]
asyncio_mode = "auto"

# Coverage configuration
[tool.coverage.run]
source = ["src/wn_dl"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__main__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# flake8 configuration
[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".venv",
    ".mypy_cache",
    ".pytest_cache",
]

[dependency-groups]
dev = [
    "pytest>=8.3.5",
]
