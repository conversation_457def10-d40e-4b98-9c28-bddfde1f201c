"""
Command Line Interface for Web Novel Scraper.

This module provides the main CLI entry point and argument parsing.
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Any, Dict, Optional

import click
import yaml
from rich.console import Console
from rich.logging import RichHandler
from rich.panel import Panel
from rich.progress import (
    BarColumn,
    Progress,
    SpinnerColumn,
    TaskProgressColumn,
    TextColumn,
    TimeElapsedColumn,
)
from rich.table import Table

from wn_dl import __version__
from wn_dl.config import get_config
from wn_dl.core.epub_generator import EPUBGenerator
from wn_dl.core.font_manager import get_font_manager
from wn_dl.core.models import ScrapingProgress
from wn_dl.core.novel_discovery import NovelDiscoveryService
from wn_dl.core.processor import NovelProcessor
from wn_dl.core.user_config import get_user_config_manager, get_user_preferences
from wn_dl.providers import list_providers, list_supported_domains

console = Console()
logger = logging.getLogger(__name__)


def setup_rich_logging(
    console: Console, verbose: bool = False, quiet: bool = False
) -> None:
    """
    Set up Rich logging handler that works well with progress bars.

    Args:
        console: Rich console instance
        verbose: Enable verbose logging
        quiet: Enable quiet mode (warnings only)
    """
    log_level = logging.DEBUG if verbose else logging.WARNING if quiet else logging.INFO

    # Configure Rich logging handler to work with progress bars
    rich_handler = RichHandler(
        console=console,
        show_time=True,
        show_path=False,
        rich_tracebacks=True,
        tracebacks_show_locals=verbose,
        markup=True,  # Enable Rich markup in log messages
        log_time_format="[%X]",  # Time format
    )

    # Set up logging with Rich handler
    logging.basicConfig(
        level=log_level,
        format="%(message)s",
        datefmt="[%X]",
        handlers=[rich_handler],
        force=True,  # Override any existing configuration
    )

    # Suppress some noisy loggers in quiet mode
    if quiet:
        logging.getLogger("urllib3").setLevel(logging.ERROR)
        logging.getLogger("aiohttp").setLevel(logging.ERROR)


@click.group()
@click.version_option(version=__version__, prog_name="wn-dl")
@click.option(
    "--with-info",
    is_flag=True,
    help="Show detailed logging information (default is silent mode)",
)
@click.option(
    "--config",
    "-c",
    help="Configuration file path",
    default=None,
)
@click.pass_context
def cli(ctx, with_info: bool, config: Optional[str]):
    """
    Web Novel Scraper - Download and convert web novels to EPUB format.

    A modular scraper that supports multiple web novel providers with
    concurrent processing and high-quality EPUB generation.
    """
    # Ensure context object exists
    ctx.ensure_object(dict)

    # Set up logging with Rich integration
    # Default is silent mode (quiet=True), unless --with-info is specified
    verbose = with_info
    quiet = not with_info

    setup_rich_logging(console, verbose, quiet)

    # Load user preferences
    try:
        user_preferences = get_user_preferences()
        ctx.obj["user_preferences"] = user_preferences
    except Exception as e:
        logger.warning(f"Could not load user preferences: {e}")
        ctx.obj["user_preferences"] = None

    # Store configuration in context
    ctx.obj["verbose"] = verbose
    ctx.obj["quiet"] = quiet
    ctx.obj["with_info"] = with_info
    ctx.obj["config"] = config


@cli.command()
@click.option(
    "--url",
    "-u",
    help="URL of the novel to scrape",
    default=None,
)
@click.option(
    "--files",
    help="Text file containing list of novel URLs (one per line)",
    type=click.Path(exists=True, dir_okay=False, path_type=Path),
    default=None,
)
@click.option(
    "--output",
    "-o",
    help="Output directory for generated files (uses user preference if not specified)",
    default=None,
    show_default=False,
)
@click.option(
    "--format",
    "-f",
    "output_format",
    type=click.Choice(["markdown", "epub", "both"]),
    default="both",
    help="Output format",
    show_default=True,
)
@click.option(
    "--provider",
    "-p",
    help="Provider to use (auto-detected if not specified)",
    default=None,
)
@click.option(
    "--max-workers",
    "-w",
    type=int,
    help="Maximum concurrent workers",
    default=None,
)
@click.option(
    "--rate-limit",
    "-r",
    type=float,
    help="Rate limit in requests per second",
    default=None,
)
@click.option(
    "--no-cover",
    is_flag=True,
    help="Skip cover image download",
)
@click.option(
    "--font",
    help="Font family to use for EPUB generation (use 'wn-dl list-fonts' to see available fonts)",
    default=None,
)
@click.pass_context
def scrape(
    ctx,
    url: Optional[str],
    files: Optional[Path],
    output: str,
    output_format: str,
    provider: Optional[str],
    max_workers: Optional[int],
    rate_limit: Optional[float],
    no_cover: bool,
    font: Optional[str],
) -> None:
    """Scrape a novel from the given URL or multiple novels from a file."""
    # Validate input parameters
    if not url and not files:
        console.print("[red]Error: Either --url or --files must be specified[/red]")
        console.print("Use --help for more information")
        sys.exit(1)

    if url and files:
        console.print("[red]Error: Cannot specify both --url and --files[/red]")
        console.print(
            "Use either --url for single novel or --files for multiple novels"
        )
        sys.exit(1)

    if files:
        # Process multiple URLs from file
        asyncio.run(
            _scrape_multiple_async(
                ctx,
                files,
                output,
                output_format,
                provider,
                max_workers,
                rate_limit,
                no_cover,
                font,
            )
        )
    else:
        # Process single URL
        asyncio.run(
            _scrape_async(
                ctx,
                url,
                output,
                output_format,
                provider,
                max_workers,
                rate_limit,
                no_cover,
                font,
            )
        )


async def _scrape_multiple_async(
    ctx,
    files: Path,
    output: Optional[str],
    output_format: str,
    provider: Optional[str],
    max_workers: Optional[int],
    rate_limit: Optional[float],
    no_cover: bool,
    font: Optional[str],
) -> None:
    """Async implementation of scrape command for multiple URLs from file."""
    verbose = ctx.obj.get("verbose", False)
    quiet = ctx.obj.get("quiet", False)

    try:
        # Read URLs from file
        with open(files, "r", encoding="utf-8") as f:
            urls = [
                line.strip()
                for line in f
                if line.strip() and not line.strip().startswith("#")
            ]

        if not urls:
            console.print(f"[red]No valid URLs found in file: {files}[/red]")
            sys.exit(1)

        total_novels = len(urls)
        successful_novels = 0
        failed_novels = 0

        if not quiet:
            console.print(
                f"[green]Found {total_novels} novels to scrape from:[/green] {files}"
            )
            console.print(f"[blue]Output directory:[/blue] {output or './output'}")
            console.print(f"[blue]Output formats:[/blue] {output_format}")
            console.print("")

        # Process each URL sequentially
        for i, url in enumerate(urls, 1):
            if not quiet:
                console.print(
                    f"[bold cyan]Processing novel {i}/{total_novels}:[/bold cyan] {url}"
                )
            else:
                console.print(f"[cyan]Novel {i}/{total_novels}:[/cyan] {url}")

            try:
                # Call the single scrape function for each URL
                await _scrape_async(
                    ctx,
                    url,
                    output,
                    output_format,
                    provider,
                    max_workers,
                    rate_limit,
                    no_cover,
                    font,
                )
                successful_novels += 1

                if not quiet:
                    console.print(f"[green]✅ Novel {i} completed successfully[/green]")
                else:
                    console.print(f"[green]✅ Completed[/green]")

            except KeyboardInterrupt:
                console.print(
                    f"\n[yellow]⏹️  Batch scraping interrupted by user at novel {i}/{total_novels}[/yellow]"
                )
                break
            except Exception as e:
                failed_novels += 1
                console.print(f"[red]❌ Novel {i} failed: {e}[/red]")
                if verbose:
                    console.print_exception()
                # Continue with next novel instead of stopping
                continue

            # Add small delay between novels to be respectful
            if i < total_novels:
                await asyncio.sleep(1)
                if not quiet:
                    console.print("")  # Add spacing between novels

        # Display final summary
        console.print(f"\n[bold blue]Batch Scraping Summary[/bold blue]")
        console.print(
            f"[green]✅ Successful:[/green] {successful_novels}/{total_novels}"
        )
        if failed_novels > 0:
            console.print(f"[red]❌ Failed:[/red] {failed_novels}/{total_novels}")

        if failed_novels > 0:
            console.print(
                f"\n[yellow]Some novels failed to scrape. Check the output above for details.[/yellow]"
            )

    except FileNotFoundError:
        console.print(f"[red]File not found: {files}[/red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Error reading file {files}: {e}[/red]")
        sys.exit(1)


async def _scrape_async(
    ctx,
    url: str,
    output: Optional[str],
    output_format: str,
    provider: Optional[str],
    max_workers: Optional[int],
    rate_limit: Optional[float],
    no_cover: bool,
    font: Optional[str],
) -> None:
    """Async implementation of scrape command."""
    verbose = ctx.obj.get("verbose", False)
    quiet = ctx.obj.get("quiet", False)
    config_file = ctx.obj.get("config")
    user_preferences = ctx.obj.get("user_preferences")

    try:
        # Apply user preferences as defaults
        if output is None and user_preferences and user_preferences.output_directory:
            output = user_preferences.output_directory
        elif output is None:
            output = "./output"

        if max_workers is None and user_preferences:
            max_workers = user_preferences.max_workers

        if rate_limit is None and user_preferences:
            rate_limit = user_preferences.rate_limit

        if font is None and user_preferences:
            font = user_preferences.font_family

        # Load configuration
        config = get_config(config_file)

        # Override config with CLI options
        if max_workers:
            config.setdefault("processing", {})["max_workers"] = max_workers
        if rate_limit:
            config.setdefault("processing", {})["rate_limit"] = rate_limit
        if no_cover:
            config.setdefault("images", {})["download_covers"] = False

        # For scrape command, automatically use EbookLib for EPUB generation
        config.setdefault("epub", {})["use_ebooklib"] = True

        # Handle font selection
        if font:
            from wn_dl.core.font_manager import validate_font_selection

            is_valid, message = validate_font_selection(font)
            if not is_valid:
                console.print(f"[yellow]Warning: {message}[/yellow]")
                console.print("[yellow]Using default font instead.[/yellow]")
            else:
                config.setdefault("epub", {})["font_family"] = font
                if not quiet:
                    console.print(f"[green]Using font:[/green] {font}")

        # Determine output formats
        if output_format == "both":
            formats = ["markdown", "epub"]
        else:
            formats = [output_format]

        if not quiet:
            console.print(f"[green]Starting scraper for:[/green] {url}")
            console.print(f"[blue]Output directory:[/blue] {output}")
            console.print(f"[blue]Output formats:[/blue] {', '.join(formats)}")

            if provider:
                console.print(f"[blue]Provider:[/blue] {provider}")
            else:
                console.print("[blue]Provider:[/blue] Auto-detect")

        # Create processor and set up progress tracking
        processor = NovelProcessor(config)

        # Progress tracking with Rich integration
        progress_task = None
        progress_display = None

        # Always show progress, but with different styles for quiet vs verbose mode
        if quiet:
            # Silent mode: Show only essential download progress
            progress_display = Progress(
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=console,
                transient=False,  # Keep progress visible
                refresh_per_second=2,  # Lower refresh rate for silent mode
            )
        else:
            # Verbose mode: Show detailed progress with spinner and time
            progress_display = Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                TimeElapsedColumn(),
                console=console,
                transient=False,  # Keep progress visible
                refresh_per_second=4,  # Higher refresh rate for verbose mode
            )

        progress_display.start()
        progress_task = progress_display.add_task("Initializing...", total=100)

        def progress_callback(progress: ScrapingProgress):
            if progress_display and progress_task is not None:
                # Update progress description based on status and mode
                if progress.status.value == "in_progress":
                    if progress.total_chapters > 0:
                        if quiet:
                            # Silent mode: Concise progress
                            description = f"Downloading {progress.completed_chapters}/{progress.total_chapters}"
                        else:
                            # Verbose mode: Detailed progress
                            description = f"Scraping chapters ({progress.completed_chapters}/{progress.total_chapters})"
                    else:
                        description = (
                            "Discovering chapters..." if not quiet else "Discovering..."
                        )
                elif progress.status.value == "completed":
                    if quiet:
                        description = (
                            f"Complete ({progress.completed_chapters} chapters)"
                        )
                    else:
                        description = (
                            f"Completed! ({progress.completed_chapters} chapters)"
                        )
                elif progress.status.value == "failed":
                    description = f"Failed after {progress.completed_chapters} chapters"
                else:
                    description = "Processing..." if not quiet else "Processing"

                progress_display.update(
                    progress_task,
                    completed=progress.completed_chapters,
                    total=max(progress.total_chapters, 1),  # Avoid division by zero
                    description=description,
                )

                # Log significant progress milestones without interfering with progress bar
                if (
                    progress.completed_chapters > 0
                    and progress.completed_chapters % 50 == 0
                ):
                    logger = logging.getLogger("wn_dl.progress")
                    logger.info(
                        f"Progress milestone: {progress.completed_chapters}/{progress.total_chapters} chapters completed"
                    )

        # Process the novel with proper cleanup
        try:
            result = await processor.process_novel_enhanced(
                novel_url=url,
                output_dir=output,
                formats=formats,
                progress_callback=progress_callback,
                save_individual_chapters=False,  # Disable individual chapters to prevent duplication
                resume_existing=False,  # Disable resume to ensure clean scraping
            )
        finally:
            # Ensure progress display is properly stopped
            if progress_display:
                progress_display.stop()
                # Add a small delay to ensure clean output
                await asyncio.sleep(0.1)

        # Display results
        if result["success"]:
            if quiet:
                # Silent mode: Show only essential completion info
                console.print(
                    f"[green]✅ {result['novel_title']} - {result['successful_chapters']} chapters downloaded[/green]"
                )
                if result.get("generated_files"):
                    files = [Path(f).name for f in result["generated_files"]]
                    console.print(f"[dim]Files: {', '.join(files)}[/dim]")
            else:
                # Verbose mode: Show detailed results table
                console.print("\n[green]✅ Scraping completed successfully![/green]")

                # Create results table
                table = Table(title="Scraping Results")
                table.add_column("Property", style="cyan")
                table.add_column("Value", style="green")

                table.add_row("Novel Title", result["novel_title"])
                table.add_row("Author", result["author"])
                table.add_row("Total Chapters", str(result["total_chapters"]))
                table.add_row("Successful Chapters", str(result["successful_chapters"]))
                table.add_row("Failed Chapters", str(result["failed_chapters"]))
                table.add_row("Processing Time", f"{result['processing_time']:.1f}s")
                table.add_row("Output Directory", result["output_directory"])

                if result.get("generated_files"):
                    files_text = "\n".join(
                        Path(f).name for f in result["generated_files"]
                    )
                    table.add_row("Generated Files", files_text)

                console.print(table)

                # Display statistics
                if result.get("statistics"):
                    stats = result["statistics"]
                    stats_panel = Panel(
                        f"Total Words: {stats['total_words']:,}\n"
                        f"Average Words per Chapter: {stats['average_words_per_chapter']:,}\n"
                        f"Estimated Reading Time: {stats['estimated_reading_time_hours']:.1f} hours",
                        title="Novel Statistics",
                        border_style="blue",
                    )
                    console.print(stats_panel)
        else:
            console.print(f"[red]❌ Scraping failed: {result['error']}[/red]")
            sys.exit(1)

    except KeyboardInterrupt:
        if progress_display:
            progress_display.stop()
        console.print("\n[yellow]⏹️  Scraping interrupted by user[/yellow]")
        sys.exit(1)
    except Exception as e:
        if progress_display:
            progress_display.stop()
        console.print(f"[red]💥 Unexpected error: {e}[/red]")
        if verbose:
            console.print_exception()
        sys.exit(1)


@cli.command("generate-epub")
@click.option(
    "--input",
    "-i",
    "input_file",
    help="Input markdown file path",
    required=True,
    type=click.Path(exists=True, dir_okay=False, path_type=Path),
)
@click.option(
    "--output",
    "-o",
    "output_dir",
    help="Output directory for EPUB file (uses user preference if not specified)",
    default=None,
    show_default=False,
    type=click.Path(path_type=Path),
)
@click.option(
    "--title",
    "-t",
    help="Novel title (overrides YAML metadata if provided)",
    default=None,
)
@click.option(
    "--author",
    "-a",
    help="Author name (overrides YAML metadata if provided)",
    default=None,
)
@click.option(
    "--description",
    "-d",
    help="Novel description (overrides YAML metadata if provided)",
    default=None,
)
@click.option(
    "--cover",
    "-c",
    help="Cover image path",
    default=None,
    type=click.Path(exists=True, dir_okay=False, path_type=Path),
)
@click.option(
    "--css",
    help="Custom CSS file path",
    default=None,
    type=click.Path(exists=True, dir_okay=False, path_type=Path),
)
@click.option(
    "--no-toc",
    is_flag=True,
    help="Disable table of contents generation",
)
@click.option(
    "--check-duplicates",
    is_flag=True,
    help="Check for and report duplicate content in markdown file",
)
@click.option(
    "--use-ebooklib",
    is_flag=True,
    help="Force use of EbookLib instead of Pandoc for EPUB generation",
)
@click.option(
    "--no-fallback",
    is_flag=True,
    help="Disable automatic fallback to EbookLib when Pandoc fails",
)
@click.option(
    "--silent",
    is_flag=True,
    help="Silent mode - show only progress bar and success message",
)
@click.option(
    "--font",
    help="Font family to use for EPUB generation (use 'wn-dl list-fonts' to see available fonts)",
    default=None,
)
@click.pass_context
def generate_epub(
    ctx,
    input_file: Path,
    output_dir: Optional[Path],
    title: Optional[str],
    author: Optional[str],
    description: Optional[str],
    cover: Optional[Path],
    css: Optional[Path],
    no_toc: bool,
    check_duplicates: bool,
    use_ebooklib: bool,
    no_fallback: bool,
    silent: bool,
    font: Optional[str],
) -> None:
    """Generate EPUB from existing markdown file."""
    verbose = ctx.obj.get("verbose", False)
    quiet = ctx.obj.get("quiet", False)
    config_file = ctx.obj.get("config")
    user_preferences = ctx.obj.get("user_preferences")

    try:
        # Apply user preferences as defaults
        if (
            output_dir is None
            and user_preferences
            and user_preferences.output_directory
        ):
            output_dir = Path(user_preferences.output_directory)
        elif output_dir is None:
            output_dir = Path("./")

        if font is None and user_preferences:
            font = user_preferences.font_family

        if not use_ebooklib and user_preferences:
            # Use user's preferred generator if not explicitly specified
            use_ebooklib = user_preferences.preferred_generator == "ebooklib"

        # Load configuration
        config = get_config(config_file)

        # Override EPUB config with CLI options
        epub_config = config.setdefault("epub", {})
        if no_toc:
            epub_config["include_toc"] = False
        # Note: if no_toc is False, we keep the config file value
        if use_ebooklib:
            epub_config["use_ebooklib"] = True
        if no_fallback:
            epub_config["ebooklib_fallback"] = False

        # Handle font selection
        if font:
            from wn_dl.core.font_manager import validate_font_selection

            is_valid, message = validate_font_selection(font)
            if not is_valid:
                console.print(f"[yellow]Warning: {message}[/yellow]")
                console.print("[yellow]Using default font instead.[/yellow]")
            else:
                epub_config["font_family"] = font
                if not quiet and not silent:
                    console.print(f"[green]Using font:[/green] {font}")

        # Create EPUB generator
        epub_generator = EPUBGenerator(config, silent=silent)

        # Check if we can proceed with EPUB generation
        if (
            not epub_generator.pandoc_available
            and not epub_config.get("use_ebooklib", False)
            and not epub_config.get("ebooklib_fallback", True)
        ):
            console.print(
                "[red]Error: Pandoc is not available and EbookLib fallback is disabled.[/red]"
            )
            console.print(
                "[yellow]Install Pandoc: https://pandoc.org/installing.html[/yellow]"
            )
            console.print(
                "[yellow]Or use --use-ebooklib to force EbookLib usage[/yellow]"
            )
            sys.exit(1)

        # Check for duplicate content if requested
        if check_duplicates:
            duplicate_info = _check_for_duplicates(input_file)
            if duplicate_info["has_duplicates"]:
                console.print("[red]⚠️  DUPLICATE CONTENT DETECTED![/red]")
                console.print(
                    f"[yellow]File size: {duplicate_info['file_size_mb']:.1f}MB[/yellow]"
                )
                console.print(
                    f"[yellow]YAML sections: {duplicate_info['yaml_sections']} (should be 2)[/yellow]"
                )
                console.print(
                    f"[yellow]Title occurrences: {duplicate_info['title_count']}[/yellow]"
                )
                console.print("")
                console.print(
                    "[red]This file appears to contain duplicate content from multiple scraping sessions.[/red]"
                )
                console.print("[yellow]RECOMMENDED SOLUTION:[/yellow]")
                console.print("1. Delete the existing novel directory")
                console.print("2. Re-scrape the novel with a clean start")
                console.print("3. Or manually clean the markdown file")
                console.print("")
                if not click.confirm("Continue with EPUB generation anyway?"):
                    sys.exit(1)

        # Extract YAML metadata from markdown file
        yaml_metadata = _extract_yaml_metadata(input_file)

        # Determine final metadata (CLI options override YAML)
        final_title = (
            title
            or yaml_metadata.get("title")
            or _extract_title_from_markdown(input_file)
        )
        if not final_title:
            final_title = input_file.stem.replace("_", " ").replace("-", " ").title()

        final_author = author or yaml_metadata.get("author")
        final_description = description or yaml_metadata.get("description")
        final_cover = (
            cover or yaml_metadata.get("cover-image") or yaml_metadata.get("cover_path")
        )

        # Prepare metadata for EPUB generation
        metadata = {"title": final_title}
        if final_author:
            metadata["author"] = final_author
        if final_description:
            metadata["description"] = final_description
        if final_cover:
            # Handle both absolute and relative cover paths
            cover_path = Path(final_cover)
            if not cover_path.is_absolute():
                # Resolve relative to markdown file directory
                cover_path = input_file.parent / cover_path
            if cover_path.exists():
                metadata["cover_path"] = str(cover_path)
            else:
                if not quiet:
                    console.print(
                        f"[yellow]Warning: Cover image not found: {cover_path}[/yellow]"
                    )

        # Add any additional YAML metadata
        for key, value in yaml_metadata.items():
            if (
                key
                not in ["title", "author", "description", "cover-image", "cover_path"]
                and value
            ):
                metadata[key] = value

        # Create output directory
        output_dir.mkdir(parents=True, exist_ok=True)

        if not quiet and not silent:
            console.print(f"[blue]Generating EPUB from:[/blue] {input_file}")
            console.print(f"[blue]Title:[/blue] {final_title}")
            if final_author:
                console.print(f"[blue]Author:[/blue] {final_author}")
            if final_description:
                console.print(
                    f"[blue]Description:[/blue] {final_description[:100]}{'...' if len(final_description) > 100 else ''}"
                )
            console.print(f"[blue]Output directory:[/blue] {output_dir}")
            if css:
                console.print(f"[blue]Using CSS:[/blue] {css}")
            elif yaml_metadata.get("epub-css"):
                console.print(
                    f"[blue]Using CSS from YAML:[/blue] {yaml_metadata.get('epub-css')}"
                )
            if metadata.get("cover_path"):
                console.print(f"[blue]Cover image:[/blue] {metadata.get('cover_path')}")

        # Generate EPUB
        result = epub_generator.generate_epub(
            str(input_file),
            output_dir,
            final_title,
            css_file=str(css) if css else None,
            metadata=metadata,
        )

        if result:
            # result is already a full path from the generator
            epub_path = Path(result) if isinstance(result, str) else result
            if not quiet and not silent:
                console.print(
                    f"[green]✅ EPUB generated successfully:[/green] {epub_path}"
                )
                if epub_path.exists():
                    console.print(
                        f"[green]File size:[/green] {epub_path.stat().st_size:,} bytes"
                    )
                else:
                    console.print(
                        "[yellow]Warning: EPUB file path may be incorrect[/yellow]"
                    )
        else:
            if not silent:
                console.print("[red]❌ Failed to generate EPUB[/red]")
            sys.exit(1)

    except Exception as e:
        console.print(f"[red]Error generating EPUB: {e}[/red]")
        if verbose:
            console.print_exception()
        sys.exit(1)


def _extract_title_from_markdown(markdown_file: Path) -> Optional[str]:
    """Extract title from markdown file (first H1 heading)."""
    try:
        with open(markdown_file, "r", encoding="utf-8") as f:
            content = f.read()

        # Skip YAML frontmatter if present
        if content.startswith("---"):
            parts = content.split("---", 2)
            if len(parts) >= 3:
                content = parts[2]

        # Find first H1 heading
        for line in content.split("\n"):
            line = line.strip()
            if line.startswith("# "):
                return line[2:].strip()
            # Stop after first few lines to avoid reading entire file
            if len(line) > 0 and not line.startswith("#"):
                break
    except Exception:
        pass
    return None


def _extract_yaml_metadata(markdown_file: Path) -> Dict[str, Any]:
    """Extract YAML frontmatter metadata from markdown file."""
    try:
        with open(markdown_file, "r", encoding="utf-8") as f:
            content = f.read()

        # Check if file starts with YAML frontmatter
        if not content.startswith("---"):
            return {}

        # Split content to extract YAML
        parts = content.split("---", 2)
        if len(parts) < 3:
            return {}

        yaml_content = parts[1].strip()
        if not yaml_content:
            return {}

        # Parse YAML
        metadata = yaml.safe_load(yaml_content)
        return metadata if isinstance(metadata, dict) else {}

    except Exception as e:
        # Log error but don't fail the entire operation
        logger.debug(f"Failed to extract YAML metadata from {markdown_file}: {e}")
        return {}


def _check_for_duplicates(markdown_file: Path) -> Dict[str, Any]:
    """
    Check for duplicate content in markdown file.

    Args:
        markdown_file: Path to markdown file to check

    Returns:
        Dictionary with duplicate detection results
    """
    try:
        file_size_mb = markdown_file.stat().st_size / (1024 * 1024)

        with open(markdown_file, "r", encoding="utf-8") as f:
            content = f.read()

        # Count YAML delimiters (should be exactly 2: opening and closing)
        yaml_sections = content.count("---")

        # Extract title from YAML to count occurrences
        title = None
        yaml_metadata = _extract_yaml_metadata(markdown_file)
        if yaml_metadata and "title" in yaml_metadata:
            title = yaml_metadata["title"]
            title_count = content.count(title) if title else 0
        else:
            title_count = 0

        # Determine if duplicates exist
        has_duplicates = (
            yaml_sections > 2  # More than one YAML frontmatter
            or (title and title_count > 10)  # Title appears too many times
            or file_size_mb > 30  # Suspiciously large file
        )

        return {
            "has_duplicates": has_duplicates,
            "file_size_mb": file_size_mb,
            "yaml_sections": yaml_sections,
            "title_count": title_count,
            "title": title,
        }

    except Exception as e:
        logger.debug(f"Failed to check for duplicates in {markdown_file}: {e}")
        return {
            "has_duplicates": False,
            "file_size_mb": 0,
            "yaml_sections": 0,
            "title_count": 0,
            "title": None,
        }


@cli.command()
def providers():
    """List supported providers and domains."""
    console.print("[bold blue]Supported Providers[/bold blue]")

    from wn_dl.providers.registry import registry

    provider_list = list_providers()

    if provider_list:
        table = Table(title="Available Providers")
        table.add_column("Provider", style="cyan")
        table.add_column("Supported Domains", style="green")

        for provider in provider_list:
            # Get provider info which includes specific domains
            provider_info = registry.get_provider_info(provider)
            if provider_info:
                provider_domains = provider_info.get("domains", [])
                table.add_row(
                    provider, ", ".join(provider_domains) if provider_domains else "N/A"
                )
            else:
                table.add_row(provider, "N/A")

        console.print(table)
    else:
        console.print("[yellow]No providers registered[/yellow]")


@cli.command("list-fonts")
def list_fonts():
    """List available fonts for EPUB generation."""
    try:
        font_manager = get_font_manager()
        available_fonts = font_manager.get_available_fonts()

        if not available_fonts:
            console.print("[yellow]No fonts available[/yellow]")
            return

        console.print("[bold blue]Available Fonts for EPUB Generation[/bold blue]\n")

        # Create table for font information
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Font Name", style="cyan", no_wrap=True)
        table.add_column("Display Name", style="green")
        table.add_column("Status", style="yellow")
        table.add_column("Variants", style="blue")

        for font_name in available_fonts:
            font_info = font_manager.get_font_info(font_name)
            if font_info:
                status = "✅ Complete" if font_info["is_complete"] else "⚠️ Incomplete"
                variants = ", ".join(font_info["variants"])

                # Highlight default font
                display_name = font_info["display_name"]
                if font_name == font_manager.get_default_font():
                    display_name = f"{display_name} [bold](default)[/bold]"

                table.add_row(font_name, display_name, status, variants)

        console.print(table)

        # Show usage examples
        console.print("\n[bold green]Usage Examples:[/bold green]")
        console.print("  wn-dl scrape -u URL --font bookerly")
        console.print("  wn-dl generate-epub --input novel.md --font bitter")

        # Show incomplete fonts warning
        incomplete_fonts = [
            font
            for font in available_fonts
            if not font_manager.get_font_info(font)["is_complete"]
        ]
        if incomplete_fonts:
            console.print(
                f"\n[yellow]Note: Incomplete fonts ({', '.join(incomplete_fonts)}) may not work properly for EPUB generation.[/yellow]"
            )

    except Exception as e:
        console.print(f"[red]Error listing fonts: {e}[/red]")


@cli.group()
def config():
    """Manage user configuration and preferences."""
    pass


@config.command("show")
def config_show():
    """Display current user configuration."""
    try:
        user_config_manager = get_user_config_manager()
        config_file = user_config_manager.get_config_file_path()

        if not config_file or not config_file.exists():
            console.print("[yellow]No user configuration file found.[/yellow]")
            console.print("Run 'wn-dl config init' to create one.")
            return

        console.print(f"[bold blue]User Configuration[/bold blue] ({config_file})\n")

        # Load and display configuration
        preferences = user_config_manager.get_preferences()

        # Create table for preferences
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Setting", style="cyan", no_wrap=True)
        table.add_column("Value", style="green")
        table.add_column("Description", style="blue")

        # Font preferences
        table.add_row(
            "font.default_family",
            preferences.font_family,
            "Default font for EPUB generation",
        )
        table.add_row(
            "font.fallback_family",
            preferences.font_fallback,
            "Fallback font if default unavailable",
        )

        # Logging preferences
        table.add_row("logging.level", preferences.log_level, "Logging verbosity level")
        table.add_row(
            "logging.file",
            str(preferences.log_file) if preferences.log_file else "console",
            "Log output destination",
        )

        # Directory preferences
        table.add_row(
            "directories.output",
            preferences.output_directory or "current",
            "Default output directory",
        )
        table.add_row(
            "directories.input",
            preferences.input_directory or "current",
            "Default input directory",
        )

        # EPUB preferences
        table.add_row(
            "epub.preferred_generator",
            preferences.preferred_generator,
            "Preferred EPUB generator",
        )
        table.add_row(
            "epub.fallback_enabled",
            str(preferences.fallback_enabled),
            "Enable generator fallback",
        )
        table.add_row(
            "epub.include_toc",
            str(preferences.include_toc),
            "Include table of contents",
        )

        # Processing preferences
        table.add_row(
            "processing.max_workers",
            str(preferences.max_workers),
            "Maximum concurrent workers",
        )
        table.add_row(
            "processing.rate_limit", str(preferences.rate_limit), "Requests per second"
        )
        table.add_row(
            "processing.timeout", str(preferences.timeout), "Request timeout (seconds)"
        )

        console.print(table)

    except Exception as e:
        console.print(f"[red]Error displaying configuration: {e}[/red]")


@config.command("set")
@click.argument("key")
@click.argument("value")
def config_set(key: str, value: str):
    """Set a configuration value."""
    try:
        user_config_manager = get_user_config_manager()

        # Convert string value to appropriate type
        converted_value = _convert_config_value(value)

        # Validate the key
        if not _validate_config_key(key):
            console.print(f"[red]Invalid configuration key: {key}[/red]")
            console.print("Use 'wn-dl config show' to see available settings.")
            return

        # Set the preference
        success = user_config_manager.set_preference(key, converted_value)

        if success:
            console.print(f"[green]✅ Set {key} = {converted_value}[/green]")
        else:
            console.print(f"[red]Failed to set {key}[/red]")

    except Exception as e:
        console.print(f"[red]Error setting configuration: {e}[/red]")


@config.command("reset")
@click.option("--confirm", is_flag=True, help="Skip confirmation prompt")
def config_reset(confirm: bool):
    """Reset configuration to defaults."""
    try:
        if not confirm:
            response = click.confirm(
                "This will reset all user preferences to defaults. Continue?"
            )
            if not response:
                console.print("Configuration reset cancelled.")
                return

        user_config_manager = get_user_config_manager()
        success = user_config_manager.reset_to_defaults()

        if success:
            console.print("[green]✅ Configuration reset to defaults[/green]")
        else:
            console.print("[red]Failed to reset configuration[/red]")

    except Exception as e:
        console.print(f"[red]Error resetting configuration: {e}[/red]")


@config.command("init")
@click.option("--force", is_flag=True, help="Overwrite existing configuration")
def config_init(force: bool):
    """Initialize user configuration with interactive setup."""
    try:
        user_config_manager = get_user_config_manager()
        config_file = user_config_manager.get_config_file_path()

        # Check if config already exists
        if config_file and config_file.exists() and not force:
            console.print(
                f"[yellow]Configuration file already exists: {config_file}[/yellow]"
            )
            console.print(
                "Use --force to overwrite or 'wn-dl config set' to modify settings."
            )
            return

        console.print("[bold blue]🔧 wn-dl Configuration Setup[/bold blue]\n")
        console.print("Let's set up your preferences. Press Enter to use defaults.\n")

        # Interactive setup
        preferences = {}

        # Font preferences
        console.print("[bold]Font Preferences[/bold]")
        font_manager = get_font_manager()
        available_fonts = font_manager.get_available_fonts()
        console.print(f"Available fonts: {', '.join(available_fonts)}")

        font_family = click.prompt(
            "Default font family",
            default="bitter",
            type=click.Choice(available_fonts, case_sensitive=False),
        )
        preferences["font"] = {
            "default_family": font_family,
            "fallback_family": "bitter",
        }

        # Logging preferences
        console.print("\n[bold]Logging Preferences[/bold]")
        log_level = click.prompt(
            "Log level",
            default="WARNING",
            type=click.Choice(
                ["DEBUG", "INFO", "WARNING", "ERROR"], case_sensitive=False
            ),
        )
        preferences["logging"] = {"level": log_level}

        # Directory preferences
        console.print("\n[bold]Directory Preferences[/bold]")
        output_dir = click.prompt(
            "Default output directory (empty for current)",
            default="",
            show_default=False,
        )
        if output_dir:
            preferences["directories"] = {"output": output_dir, "auto_create": True}

        # EPUB preferences
        console.print("\n[bold]EPUB Generator Preferences[/bold]")
        generator = click.prompt(
            "Preferred EPUB generator",
            default="pandoc",
            type=click.Choice(["pandoc", "ebooklib"], case_sensitive=False),
        )
        preferences["epub"] = {
            "preferred_generator": generator,
            "fallback_enabled": True,
            "include_toc": True,
        }

        # Save configuration
        config_data = {"preferences": preferences}
        success = user_config_manager.save_user_config(config_data, create_backup=False)

        if success:
            config_file = user_config_manager.get_config_file_path()
            console.print(f"\n[green]✅ Configuration saved to: {config_file}[/green]")
            console.print("Use 'wn-dl config show' to view your settings.")
            console.print("Use 'wn-dl config set <key> <value>' to modify settings.")
        else:
            console.print("\n[red]Failed to save configuration[/red]")

    except Exception as e:
        console.print(f"[red]Error initializing configuration: {e}[/red]")


@config.command("validate")
def config_validate():
    """Validate current configuration."""
    try:
        user_config_manager = get_user_config_manager()
        config_file = user_config_manager.get_config_file_path()

        if not config_file or not config_file.exists():
            console.print("[yellow]No user configuration file found.[/yellow]")
            return

        console.print(
            f"[bold blue]Validating Configuration[/bold blue] ({config_file})\n"
        )

        # Load configuration
        preferences = user_config_manager.get_preferences()

        issues = []

        # Validate font
        font_manager = get_font_manager()
        if not font_manager.is_font_available(preferences.font_family):
            issues.append(f"Font '{preferences.font_family}' is not available")

        # Validate directories
        if preferences.output_directory:
            output_path = Path(preferences.output_directory).expanduser()
            if not output_path.exists() and not preferences.auto_create_dirs:
                issues.append(f"Output directory does not exist: {output_path}")

        # Validate log level
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if preferences.log_level not in valid_levels:
            issues.append(f"Invalid log level: {preferences.log_level}")

        # Validate generator
        if preferences.preferred_generator not in ["pandoc", "ebooklib"]:
            issues.append(f"Invalid EPUB generator: {preferences.preferred_generator}")

        # Display results
        if issues:
            console.print("[red]❌ Configuration has issues:[/red]")
            for issue in issues:
                console.print(f"  • {issue}")
        else:
            console.print("[green]✅ Configuration is valid[/green]")

    except Exception as e:
        console.print(f"[red]Error validating configuration: {e}[/red]")


def _convert_config_value(value: str) -> Any:
    """Convert string value to appropriate type."""
    # Boolean values
    if value.lower() in ("true", "yes", "1", "on"):
        return True
    elif value.lower() in ("false", "no", "0", "off"):
        return False

    # Numeric values
    try:
        if "." in value:
            return float(value)
        else:
            return int(value)
    except ValueError:
        pass

    # String value
    return value


def _validate_config_key(key: str) -> bool:
    """Validate configuration key."""
    valid_keys = {
        "font.default_family",
        "font.fallback_family",
        "logging.level",
        "logging.format",
        "logging.file",
        "directories.output",
        "directories.input",
        "directories.working",
        "directories.auto_create",
        "epub.preferred_generator",
        "epub.fallback_enabled",
        "epub.include_toc",
        "epub.compression",
        "processing.max_workers",
        "processing.rate_limit",
        "processing.timeout",
        "images.download_covers",
        "images.quality",
        "images.format",
    }
    return key in valid_keys


@cli.command()
@click.option(
    "--check-pandoc",
    is_flag=True,
    help="Check if Pandoc is installed",
)
def info(check_pandoc: bool):
    """Show system information and dependencies."""
    console.print(f"[bold blue]Web Novel Scraper v{__version__}[/bold blue]")

    info_table = Table(title="System Information")
    info_table.add_column("Component", style="cyan")
    info_table.add_column("Status", style="green")

    # Python version
    info_table.add_row(
        "Python Version",
        f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
    )

    # Check Pandoc
    if check_pandoc:
        from wn_dl.core.epub_generator import EPUBGenerator

        epub_gen = EPUBGenerator({})
        pandoc_version = epub_gen.get_pandoc_version()

        if pandoc_version:
            info_table.add_row("Pandoc", f"✅ {pandoc_version}")
        else:
            info_table.add_row("Pandoc", "❌ Not found")

    # Providers
    provider_count = len(list_providers())
    info_table.add_row("Registered Providers", str(provider_count))

    console.print(info_table)


@cli.group()
@click.pass_context
def novels(ctx):
    """Manage scraped novels and EPUB generation."""
    pass


@novels.command("list")
@click.option(
    "--directory",
    "-d",
    help="Directory to scan for novels (uses user preference if not specified)",
    type=click.Path(exists=True, file_okay=False, dir_okay=True, path_type=Path),
    default=None,
)
@click.option(
    "--no-epub",
    is_flag=True,
    help="Show only novels without EPUB files",
)
@click.option(
    "--format",
    "output_format",
    type=click.Choice(["table", "simple", "json"]),
    default="table",
    help="Output format",
)
@click.pass_context
def list_novels(ctx, directory: Optional[Path], no_epub: bool, output_format: str):
    """List all scraped novels in the output directory."""
    try:
        # Get user preferences for default directory
        user_preferences = ctx.obj.get("user_preferences")

        # Determine directory to scan
        if directory is None:
            if user_preferences and user_preferences.output_directory:
                directory = Path(user_preferences.output_directory)
            else:
                directory = Path.cwd()

        # Initialize discovery service
        discovery_service = NovelDiscoveryService(str(directory))

        # Discover novels
        if no_epub:
            novels = discovery_service.get_novels_without_epub()
        else:
            novels = discovery_service.discover_novels()

        if not novels:
            console.print(f"[yellow]No novels found in {directory}[/yellow]")
            return

        # Display results based on format
        if output_format == "json":
            import json

            novels_data = []
            for novel in novels:
                novels_data.append(
                    {
                        "title": novel.title,
                        "author": novel.author,
                        "directory": str(novel.directory),
                        "markdown_file": str(novel.markdown_file),
                        "markdown_size": novel.markdown_size,
                        "has_epub": novel.has_epub,
                        "epub_file": str(novel.epub_file) if novel.epub_file else None,
                        "epub_size": novel.epub_size,
                        "chapter_count": novel.chapter_count,
                        "status": novel.status,
                        "modified_at": (
                            novel.modified_at.isoformat() if novel.modified_at else None
                        ),
                    }
                )
            console.print(json.dumps(novels_data, indent=2))

        elif output_format == "simple":
            for novel in novels:
                epub_status = "✅" if novel.has_epub else "❌"
                console.print(f"{epub_status} {novel.title} by {novel.author}")

        else:  # table format
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Title", style="cyan", no_wrap=False, max_width=40)
            table.add_column("Author", style="green", max_width=20)
            table.add_column("Status", style="blue", max_width=12)
            table.add_column("Chapters", justify="right", style="yellow")
            table.add_column("MD Size", justify="right", style="white")
            table.add_column("EPUB", justify="center", style="green")
            table.add_column("Modified", style="dim", max_width=12)

            for novel in novels:
                # Format file size
                md_size = _format_file_size(novel.markdown_size)

                # EPUB status
                epub_status = "✅" if novel.has_epub else "❌"

                # Format modified date
                modified = (
                    novel.modified_at.strftime("%Y-%m-%d")
                    if novel.modified_at
                    else "Unknown"
                )

                # Chapter count
                chapters = (
                    str(novel.chapter_count) if novel.chapter_count else "Unknown"
                )

                # Status
                status = novel.status or "Unknown"

                table.add_row(
                    novel.title,
                    novel.author,
                    status,
                    chapters,
                    md_size,
                    epub_status,
                    modified,
                )

            console.print(
                f"\n[bold blue]Found {len(novels)} novels in {directory}[/bold blue]\n"
            )
            console.print(table)

            # Summary
            epub_count = sum(1 for novel in novels if novel.has_epub)
            console.print(f"\n[green]📚 {len(novels)} novels total[/green]")
            console.print(f"[green]📖 {epub_count} with EPUB files[/green]")
            console.print(
                f"[yellow]📝 {len(novels) - epub_count} without EPUB files[/yellow]"
            )

    except Exception as e:
        console.print(f"[red]Error listing novels: {e}[/red]")
        sys.exit(1)


@novels.command("regenerate")
@click.option(
    "--name",
    "-n",
    help="Novel name to regenerate (searches by title)",
    default=None,
)
@click.option(
    "--input",
    "-i",
    "input_file",
    help="Direct path to markdown file",
    type=click.Path(exists=True, dir_okay=False, path_type=Path),
    default=None,
)
@click.option(
    "--directory",
    "-d",
    help="Directory to search for novels (uses user preference if not specified)",
    type=click.Path(exists=True, file_okay=False, dir_okay=True, path_type=Path),
    default=None,
)
@click.option(
    "--output",
    "-o",
    help="Output directory for EPUB file (uses novel directory if not specified)",
    type=click.Path(file_okay=False, dir_okay=True, path_type=Path),
    default=None,
)
@click.option(
    "--all",
    "regenerate_all",
    is_flag=True,
    help="Regenerate EPUB for all novels",
)
@click.option(
    "--missing-epub",
    is_flag=True,
    help="Regenerate only novels without EPUB files",
)
@click.option(
    "--font",
    help="Font family to use for EPUB (overrides user preference)",
    default=None,
)
@click.option(
    "--use-ebooklib",
    is_flag=True,
    help="Force use of EbookLib instead of Pandoc",
)
@click.option(
    "--silent",
    is_flag=True,
    help="Silent mode - minimal output",
)
@click.pass_context
def regenerate_novel(
    ctx,
    name: Optional[str],
    input_file: Optional[Path],
    directory: Optional[Path],
    output: Optional[Path],
    regenerate_all: bool,
    missing_epub: bool,
    font: Optional[str],
    use_ebooklib: bool,
    silent: bool,
):
    """Regenerate EPUB for novels (single or bulk)."""
    try:
        # Validate input parameters
        options_count = sum(
            [bool(name), bool(input_file), regenerate_all, missing_epub]
        )
        if options_count == 0:
            console.print(
                "[red]Error: Must specify one of: --name, --input, --all, or --missing-epub[/red]"
            )
            console.print("Use --help for more information")
            sys.exit(1)

        if options_count > 1:
            console.print("[red]Error: Cannot specify multiple operation modes[/red]")
            sys.exit(1)

        # Get user preferences
        user_preferences = ctx.obj.get("user_preferences")

        # Determine search directory
        if directory is None:
            if user_preferences and user_preferences.output_directory:
                directory = Path(user_preferences.output_directory)
            else:
                directory = Path.cwd()

        discovery_service = NovelDiscoveryService(str(directory))

        # Handle different operation modes
        if input_file:
            # Single file mode
            novels_to_process = [{"input_file": input_file, "output_dir": output}]

        elif name:
            # Single novel by name
            novel_info = discovery_service.find_novel_by_name(name)

            if not novel_info:
                console.print(f"[red]Novel '{name}' not found in {directory}[/red]")
                console.print("Use 'wn-dl novels list' to see available novels")
                sys.exit(1)

            novels_to_process = [
                {
                    "input_file": novel_info.markdown_file,
                    "output_dir": output or novel_info.directory,
                    "title": novel_info.title,
                }
            ]

        elif regenerate_all:
            # All novels
            all_novels = discovery_service.discover_novels()
            novels_to_process = [
                {
                    "input_file": novel.markdown_file,
                    "output_dir": output or novel.directory,
                    "title": novel.title,
                }
                for novel in all_novels
            ]

        elif missing_epub:
            # Only novels without EPUB
            novels_without_epub = discovery_service.get_novels_without_epub()
            novels_to_process = [
                {
                    "input_file": novel.markdown_file,
                    "output_dir": output or novel.directory,
                    "title": novel.title,
                }
                for novel in novels_without_epub
            ]

        if not novels_to_process:
            console.print("[yellow]No novels found to process[/yellow]")
            return

        # Process novels
        success_count = 0
        error_count = 0

        # Use progress bar for bulk operations
        if len(novels_to_process) > 1 and not silent:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                TextColumn("•"),
                TimeElapsedColumn(),
                console=console,
                transient=False,
            ) as progress:
                task = progress.add_task(
                    f"Processing {len(novels_to_process)} novels...",
                    total=len(novels_to_process),
                )

                for novel_data in novels_to_process:
                    title = novel_data.get("title", novel_data["input_file"].stem)
                    progress.update(task, description=f"Processing: {title}")

                    try:
                        # Create a proper context for generate_epub
                        import click

                        mock_ctx = click.Context(generate_epub)
                        mock_ctx.obj = ctx.obj

                        # Call generate_epub with our parameters
                        generate_epub(
                            mock_ctx,
                            input_file=novel_data["input_file"],
                            output_dir=novel_data["output_dir"],
                            title=None,
                            author=None,
                            description=None,
                            cover=None,
                            css=None,
                            no_toc=False,
                            check_duplicates=False,
                            use_ebooklib=use_ebooklib,
                            no_fallback=False,
                            silent=True,  # Always silent for bulk operations with progress bar
                            font=font,
                        )
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        progress.console.print(
                            f"[red]Error processing {title}: {e}[/red]"
                        )

                    progress.advance(task)
        else:
            # Single novel or silent mode
            for i, novel_data in enumerate(novels_to_process, 1):
                try:
                    if not silent:
                        console.print(
                            f"[blue]Regenerating EPUB for: {novel_data['input_file'].stem}[/blue]"
                        )

                    # Create a proper context for generate_epub
                    import click

                    mock_ctx = click.Context(generate_epub)
                    mock_ctx.obj = ctx.obj

                    # Call generate_epub with our parameters
                    generate_epub(
                        mock_ctx,
                        input_file=novel_data["input_file"],
                        output_dir=novel_data["output_dir"],
                        title=None,
                        author=None,
                        description=None,
                        cover=None,
                        css=None,
                        no_toc=False,
                        check_duplicates=False,
                        use_ebooklib=use_ebooklib,
                        no_fallback=False,
                        silent=silent,
                        font=font,
                    )
                    success_count += 1

                except Exception as e:
                    error_count += 1
                    if not silent:
                        title = novel_data.get("title", novel_data["input_file"].stem)
                        console.print(f"[red]Error processing {title}: {e}[/red]")

        # Summary for bulk operations
        if len(novels_to_process) > 1 and not silent:
            console.print(
                f"\n[green]✅ Successfully processed: {success_count}[/green]"
            )
            if error_count > 0:
                console.print(f"[red]❌ Failed: {error_count}[/red]")
            console.print(f"[blue]📚 Total: {len(novels_to_process)}[/blue]")

    except Exception as e:
        if not silent:
            console.print(f"[red]Error regenerating novel: {e}[/red]")
        sys.exit(1)


def _format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def main():
    """Main entry point for the CLI."""
    cli()


if __name__ == "__main__":
    main()
