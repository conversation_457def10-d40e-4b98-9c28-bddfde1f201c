"""
Novel Discovery Service for finding and managing scraped novels.

This module provides functionality to scan output directories and identify
scraped novels, extract their metadata, and manage novel collections.
"""

import os
import re
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import yaml

from .models import NovelMetadata


@dataclass
class NovelInfo:
    """Information about a discovered novel."""

    # Basic information
    title: str
    author: str
    directory: Path
    markdown_file: Path

    # File information
    markdown_size: int
    epub_file: Optional[Path] = None
    epub_size: Optional[int] = None
    cover_file: Optional[Path] = None

    # Metadata
    description: Optional[str] = None
    genres: List[str] = field(default_factory=list)
    status: Optional[str] = None
    chapter_count: Optional[int] = None

    # Timestamps
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    epub_created_at: Optional[datetime] = None

    # Status flags
    has_epub: bool = False
    is_complete: bool = False
    has_cover: bool = False


class NovelDiscoveryService:
    """Service for discovering and managing scraped novels."""

    def __init__(self, output_directory: Optional[str] = None):
        """
        Initialize the novel discovery service.

        Args:
            output_directory: Base output directory to scan for novels
        """
        self.output_directory = (
            Path(output_directory) if output_directory else Path.cwd()
        )

    def discover_novels(self, directory: Optional[Path] = None) -> List[NovelInfo]:
        """
        Discover all novels in the specified directory.

        Args:
            directory: Directory to scan (uses default if not provided)

        Returns:
            List of discovered novels
        """
        scan_dir = directory or self.output_directory

        if not scan_dir.exists() or not scan_dir.is_dir():
            return []

        novels = []

        # Scan for novel directories
        for item in scan_dir.iterdir():
            if item.is_dir() and not item.name.startswith("."):
                novel_info = self._analyze_novel_directory(item)
                if novel_info:
                    novels.append(novel_info)

        return sorted(novels, key=lambda x: x.title.lower())

    def find_novel_by_name(
        self, name: str, directory: Optional[Path] = None
    ) -> Optional[NovelInfo]:
        """
        Find a specific novel by name.

        Args:
            name: Novel name to search for
            directory: Directory to search in

        Returns:
            NovelInfo if found, None otherwise
        """
        novels = self.discover_novels(directory)

        # Try exact match first
        for novel in novels:
            if novel.title.lower() == name.lower():
                return novel

        # Try partial match
        for novel in novels:
            if name.lower() in novel.title.lower():
                return novel

        return None

    def get_novels_without_epub(
        self, directory: Optional[Path] = None
    ) -> List[NovelInfo]:
        """
        Get all novels that don't have EPUB files.

        Args:
            directory: Directory to scan

        Returns:
            List of novels without EPUB files
        """
        novels = self.discover_novels(directory)
        return [novel for novel in novels if not novel.has_epub]

    def get_novels_modified_after(
        self, date: datetime, directory: Optional[Path] = None
    ) -> List[NovelInfo]:
        """
        Get novels modified after a specific date.

        Args:
            date: Cutoff date
            directory: Directory to scan

        Returns:
            List of novels modified after the date
        """
        novels = self.discover_novels(directory)
        return [
            novel for novel in novels if novel.modified_at and novel.modified_at > date
        ]

    def _analyze_novel_directory(self, directory: Path) -> Optional[NovelInfo]:
        """
        Analyze a directory to determine if it contains a novel.

        Args:
            directory: Directory to analyze

        Returns:
            NovelInfo if valid novel directory, None otherwise
        """
        # Look for markdown files
        markdown_files = list(directory.glob("*.md"))
        if not markdown_files:
            return None

        # Use the first markdown file found (typically novel.md)
        markdown_file = markdown_files[0]

        # Extract metadata from markdown file
        metadata = self._extract_metadata_from_markdown(markdown_file)
        if not metadata:
            return None

        # Get file information
        markdown_stat = markdown_file.stat()

        # Look for EPUB file
        epub_files = list(directory.glob("*.epub"))
        epub_file = epub_files[0] if epub_files else None
        epub_size = epub_file.stat().st_size if epub_file else None
        epub_created_at = (
            datetime.fromtimestamp(epub_file.stat().st_ctime) if epub_file else None
        )

        # Look for cover image
        cover_patterns = ["cover.*", "*.jpg", "*.jpeg", "*.png", "*.webp"]
        cover_file = None
        for pattern in cover_patterns:
            covers = list(directory.glob(pattern))
            if covers:
                cover_file = covers[0]
                break

        return NovelInfo(
            title=metadata.get("title", directory.name),
            author=metadata.get("author", "Unknown"),
            directory=directory,
            markdown_file=markdown_file,
            markdown_size=markdown_stat.st_size,
            epub_file=epub_file,
            epub_size=epub_size,
            cover_file=cover_file,
            description=metadata.get("description"),
            genres=metadata.get("genres", []),
            status=metadata.get("status"),
            chapter_count=metadata.get("chapter_count"),
            created_at=datetime.fromtimestamp(markdown_stat.st_ctime),
            modified_at=datetime.fromtimestamp(markdown_stat.st_mtime),
            epub_created_at=epub_created_at,
            has_epub=epub_file is not None,
            has_cover=cover_file is not None,
            is_complete=metadata.get("status", "").lower() in ["completed", "complete"],
        )

    def _extract_metadata_from_markdown(
        self, markdown_file: Path
    ) -> Optional[Dict[str, Any]]:
        """
        Extract metadata from markdown file's YAML frontmatter.

        Args:
            markdown_file: Path to markdown file

        Returns:
            Dictionary of metadata or None if extraction failed
        """
        try:
            with open(markdown_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Look for YAML frontmatter
            if not content.startswith("---"):
                return None

            # Find the end of frontmatter
            end_match = re.search(r"\n---\n", content)
            if not end_match:
                return None

            # Extract and parse YAML
            yaml_content = content[3 : end_match.start()]
            metadata = yaml.safe_load(yaml_content)

            if not metadata:
                metadata = {}

            # Count chapters in content using multiple patterns
            chapter_patterns = [
                r"^#+ Chapter \d+",  # Standard chapter headers
                r"^## Chapter \d+",  # Level 2 headers
                r"# Chapter \d+",  # Level 1 headers
                r"{#chapter-\d+",  # Pandoc-style chapter IDs
            ]

            chapter_count = 0
            for pattern in chapter_patterns:
                matches = re.findall(pattern, content, re.MULTILINE | re.IGNORECASE)
                if matches:
                    chapter_count = max(chapter_count, len(matches))

            if chapter_count > 0:
                metadata["chapter_count"] = chapter_count

            # Extract word count estimate
            text_content = re.sub(
                r"^---.*?^---", "", content, flags=re.MULTILINE | re.DOTALL
            )
            words = len(text_content.split())
            metadata["word_count"] = words

            return metadata

        except Exception:
            return None
